// Import necessary packages
import 'package:flutter/material.dart';

// Define the category enum here
enum QuizCategory { islamic, math, historical, videoGames, sports, literature, animals, plants, cars } // Added animals, plants, and cars

/// نموذج السؤال
class QuestionModel {
  final String question; // نص السؤال
  final List<String> options; // خيارات الإجابة
  final String correctAnswer; // الإجابة الصحيحة (يجب أن تكون مطابقة لأحد الخيارات)

  QuestionModel({
    required this.question,
    required this.options,
    required this.correctAnswer,
  });
}

/// نموذج المستوى
class LevelModel {
  final int level; // رقم المستوى
  final String title; // عنوان المستوى
  final List<Color> backgroundGradient; // تدرج ألوان الخلفية
  final List<QuestionModel> questions; // أسئلة المستوى
  bool isUnlocked; // هل المستوى مفتوح؟
  int stars; // عدد النجوم المكتسبة (0-3)
  int highScore; // أعلى نتيجة محققة
  final QuizCategory category; // فئة المستوى

  LevelModel({
    required this.level,
    required this.title,
    required this.backgroundGradient,
    required this.questions,
    required this.category,
    this.isUnlocked = false,
    this.stars = 0,
    this.highScore = 0,
  });
}

/// --- 1. قائمة المستويات الإسلامية ---
final List<LevelModel> islamicLevels = [
  // المستوى 1: أساسيات الإسلام
  LevelModel(
    level: 1,
    title: "أساسيات الإسلام",
    category: QuizCategory.islamic,
    isUnlocked: true, // المستوى الأول مفتوح افتراضيًا
    backgroundGradient: const [
      Color(0xFF011F4B),
      Color(0xFF03396C),
      Color(0xFF005B96),
      Color(0xFF6497B1),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد أركان الإسلام؟",
        options: ["3 أركان", "4 أركان", "5 أركان", "6 أركان"],
        correctAnswer: "5 أركان",
      ),
      QuestionModel(
        question: "ما هو الركن الأول من أركان الإسلام؟",
        options: ["الصلاة", "الصوم", "الشهادتان", "الزكاة"],
        correctAnswer: "الشهادتان",
      ),
      QuestionModel(
        question: "كم عدد الصلوات المفروضة في اليوم والليلة؟",
        options: ["3 صلوات", "4 صلوات", "5 صلوات", "6 صلوات"],
        correctAnswer: "5 صلوات",
      ),
      QuestionModel(
        question: "في أي شهر فرض الصيام على المسلمين؟",
        options: ["شوال", "رمضان", "شعبان", "رجب"],
        correctAnswer: "رمضان",
      ),
      QuestionModel(
        question: "ما هو البيت الذي يحج إليه المسلمون؟",
        options: ["المسجد الأقصى", "المسجد النبوي", "الكعبة المشرفة", "مسجد قباء"],
        correctAnswer: "الكعبة المشرفة",
      ),
      QuestionModel(
        question: "من هو خاتم الأنبياء والمرسلين؟",
        options: [
          "سيدنا إبراهيم عليه السلام",
          "سيدنا موسى عليه السلام",
          "سيدنا عيسى عليه السلام",
          "سيدنا محمد صلى الله عليه وسلم"
        ],
        correctAnswer: "سيدنا محمد صلى الله عليه وسلم",
      ),
      QuestionModel(
        question: "ما هما الشهادتان؟",
        options: [
          "أن لا إله إلا الله وأن محمداً رسول الله",
          "الإيمان بالله وملائكته",
          "الصلاة والصوم",
          "الحج والزكاة"
        ],
        correctAnswer: "أن لا إله إلا الله وأن محمداً رسول الله",
      ),
      QuestionModel(
        question: "متى تجب الزكاة على المسلم؟",
        options: [
          "إذا بلغ المال النصاب وحال عليه الحول",
          "كل شهر",
          "كل سنة هجرية",
          "مرة واحدة في العمر"
        ],
        correctAnswer: "إذا بلغ المال النصاب وحال عليه الحول",
      ),
      QuestionModel(
        question: "كم عدد أركان الإيمان؟",
        options: ["4 أركان", "5 أركان", "6 أركان", "7 أركان"],
        correctAnswer: "6 أركان",
      ),
      QuestionModel(
        question: "متى فرضت الصلاة على المسلمين؟",
        options: [
          "في السنة الأولى للهجرة",
          "في ليلة الإسراء والمعراج",
          "بعد فتح مكة",
          "في غزوة بدر"
        ],
        correctAnswer: "في ليلة الإسراء والمعراج",
      ),
    ],
  ),

  // المستوى 2: القرآن الكريم
  LevelModel(
    level: 2,
    title: "القرآن الكريم",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF1A237E),
      Color(0xFF283593),
      Color(0xFF3949AB),
      Color(0xFF5C6BC0),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد سور القرآن الكريم؟",
        options: ["104 سورة", "114 سورة", "124 سورة", "144 سورة"],
        correctAnswer: "114 سورة",
      ),
      QuestionModel(
        question: "ما هي أول سورة نزلت في القرآن الكريم؟",
        options: ["سورة العلق", "سورة الفاتحة", "سورة البقرة", "سورة المدثر"],
        correctAnswer: "سورة العلق",
      ),
      QuestionModel(
        question: "ما هي أقصر سورة في القرآن الكريم؟",
        options: ["الإخلاص", "الكوثر", "النصر", "العصر"],
        correctAnswer: "الكوثر",
      ),
      QuestionModel(
        question: "كم عدد أجزاء القرآن الكريم؟",
        options: ["20 جزءا", "30 جزءا", "40 جزءا", "50 جزءا"],
        correctAnswer: "30 جزءا",
      ),
      QuestionModel(
        question: "من هو أول من جمع القرآن الكريم في مصحف واحد؟",
        options: [
          "أبو بكر الصديق رضي الله عنه",
          "عمر بن الخطاب رضي الله عنه",
          "عثمان بن عفان رضي الله عنه",
          "علي بن أبي طالب رضي الله عنه"
        ],
        correctAnswer: "أبو بكر الصديق رضي الله عنه",
      ),
      QuestionModel(
        question: "ما السورة التي تسمى بقلب القرآن؟",
        options: ["الإخلاص", "يس", "الرحمن", "الكهف"],
        correctAnswer: "يس",
      ),
      QuestionModel(
        question: "ما السورة التي تسمى بأم القرآن؟",
        options: ["الفاتحة", "البقرة", "آل عمران", "النساء"],
        correctAnswer: "الفاتحة",
      ),
      QuestionModel(
        question: "ما هي السورة التي نزلت كاملة؟",
        options: ["النصر", "الإخلاص", "الفاتحة", "الكوثر"],
        correctAnswer: "الفاتحة",
      ),
      QuestionModel(
        question: "كم عدد احزاب القرآن الكريم؟",
        options: ["30 حزبا", "60 حزبا", "90 حزبا", "120 حزبا"],
        correctAnswer: "60 حزبا",
      ),
      QuestionModel(
        question: "ما هي السورة التي ذكرت البسملة مرتين؟",
        options: ["النمل", "البقرة", "يس", "الرحمن"],
        correctAnswer: "النمل",
      ),
    ],
  ),

  // المستوى 3: السيرة النبوية
  LevelModel(
    level: 3,
    title: "السيرة النبوية",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF004D40),
      Color(0xFF00695C),
      Color(0xFF00796B),
      Color(0xFF00897B),
    ],
    questions: [
      QuestionModel(
        question: "متى ولد النبي محمد صلى الله عليه وسلم؟",
        options: ["عام الفيل", "عام الحزن", "عام الرمادة", "عام الوفود"],
        correctAnswer: "عام الفيل",
      ),
      QuestionModel(
        question: "من هي أم النبي محمد صلى الله عليه وسلم؟",
        options: ["آمنة بنت وهب", "حليمة السعدية", "خديجة بنت خويلد", "فاطمة بنت أسد"],
        correctAnswer: "آمنة بنت وهب",
      ),
      QuestionModel(
        question: "كم كان عمر النبي صلى الله عليه وسلم عندما توفيت أمه؟",
        options: ["4 سنوات", "6 سنوات", "8 سنوات", "10 سنوات"],
        correctAnswer: "6 سنوات",
      ),
      QuestionModel(
        question: "من هي أول زوجة للنبي محمد صلى الله عليه وسلم؟",
        options: ["عائشة", "خديجة", "حفصة", "زينب"],
        correctAnswer: "خديجة",
      ),
      QuestionModel(
        question: "كم كان عمر النبي صلى الله عليه وسلم عندما بدأ الوحي؟",
        options: ["30 عاما", "40 عاما", "50 عاما", "60 عاما"],
        correctAnswer: "40 عاما",
      ),
      QuestionModel(
        question: "من هي مرضعة النبي صلى الله عليه وسلم؟",
        options: ["حليمة السعدية", "خديجة بنت خويلد", "آمنة بنت وهب", "أم أيمن"],
        correctAnswer: "حليمة السعدية",
      ),
      QuestionModel(
        question: "كم استمرت الدعوة السرية للإسلام؟",
        options: ["سنة", "3 سنوات", "5 سنوات", "10 سنوات"],
        correctAnswer: "3 سنوات",
      ),
      QuestionModel(
        question: "في أي عام من البعثة النبوية كانت الهجرة إلى المدينة المنورة؟",
        options: ["العام الثامن", "العام العاشر", "العام الثاني عشر", "العام الثالث عشر"],
        correctAnswer: "العام الثالث عشر",
      ),
      QuestionModel(
        question: "متى توفي النبي صلى الله عليه وسلم؟",
        options: ["9 هـ", "10 هـ", "11 هـ", "12 هـ"],
        correctAnswer: "11 هـ",
      ),
      QuestionModel(
        question: "كم كان عمر النبي صلى الله عليه وسلم عندما توفي؟",
        options: ["60 عاما", "63 عاما", "65 عاما", "70 عاما"],
        correctAnswer: "63 عاما",
      ),
    ],
  ),

  // المستويات الباقية للفئة الإسلامية (4-10)
  LevelModel(
    level: 4,
    title: "العبادات والمعاملات",
    category: QuizCategory.islamic, // Assign category
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF7B1FA2),
      Color(0xFF8E24AA),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد ركعات صلاة الفجر؟",
        options: ["ركعتان", "ثلاث ركعات", "أربع ركعات", "خمس ركعات"],
        correctAnswer: "ركعتان",
      ),
      QuestionModel(
        question: "كم عدد فرائض الوضوء؟",
        options: ["3 فرائض", "4 فرائض", "6 فرائض", "8 فرائض"],
        correctAnswer: "6 فرائض",
      ),
      QuestionModel(
        question: "متى يجب الغسل على المسلم؟",
        options: [
          "بعد الجنابة",
          "بعد الوضوء",
          "بعد صلاة الجمعة",
          "قبل قراءة القرآن"
        ],
        correctAnswer: "بعد الجنابة",
      ),
      QuestionModel(
        question: "ما حكم صلاة الوتر؟",
        options: ["فرض", "واجب", "سنة", "مباح"],
        correctAnswer: "سنة",
      ),
      QuestionModel(
        question: "كم نسبة زكاة المال؟",
        options: ["1%", "2.5%", "5%", "10%"],
        correctAnswer: "2.5%",
      ),
      QuestionModel(
        question: "ما هي شروط صحة الصلاة؟",
        options: [
          "الإسلام والعقل والنية",
          "الطهارة واستقبال القبلة وستر العورة",
          "الغسل والوضوء",
          "القيام والركوع والسجود"
        ],
        correctAnswer: "الطهارة واستقبال القبلة وستر العورة",
      ),
      QuestionModel(
        question: "ما حكم بيع الغرر في الإسلام؟",
        options: ["مباح", "مكروه", "حرام", "جائز بشروط"],
        correctAnswer: "حرام",
      ),
      QuestionModel(
        question: "أي مما يلي مفسد للصوم؟",
        options: [
          "الأكل ناسياً",
          "الاغتسال",
          "الأكل والشرب عمداً",
          "السواك"
        ],
        correctAnswer: "الأكل والشرب عمداً",
      ),
      QuestionModel(
        question: "ما حكم قراءة الفاتحة في الصلاة؟",
        options: ["سنة", "فرض", "واجب", "مستحب"],
        correctAnswer: "فرض",
      ),
      QuestionModel(
        question: "ما هي أركان الصلاة الصحيحة؟",
        options: [
          "14 ركناً",
          "12 ركناً",
          "10 أركان",
          "8 أركان"
        ],
        correctAnswer: "14 ركناً",
      ),
    ],
  ),

  // المستوى 5: الفقه الإسلامي
  LevelModel(
    level: 5,
    title: "الفقه الإسلامي",
    category: QuizCategory.islamic, // Assign category
    backgroundGradient: const [
      Color(0xFF880E4F),
      Color(0xFFA01555),
      Color(0xFFC2185B),
      Color(0xFFD81B60),
    ],
    questions: [
      QuestionModel(
        question: "ما هي المذاهب الفقهية الأربعة في أهل السنة والجماعة؟",
        options: [
          "الحنفي والشافعي والمالكي والحنبلي",
          "الشافعي والإمامي والزيدي والظاهري",
          "الحنفي والمالكي والإباضي والجعفري",
          "الظاهري والحنبلي والمالكي والإمامي"
        ],
        correctAnswer: "الحنفي والشافعي والمالكي والحنبلي",
      ),
      QuestionModel(
        question: "من هو مؤسس المذهب الشافعي؟",
        options: [
          "أبو حنيفة النعمان",
          "الإمام مالك بن أنس",
          "الإمام الشافعي",
          "الإمام أحمد بن حنبل"
        ],
        correctAnswer: "الإمام الشافعي",
      ),
      QuestionModel(
        question: "ما هو تعريف القياس في أصول الفقه؟",
        options: [
          "حكم العلماء في مسألة معينة",
          "إلحاق فرع بأصل في الحكم لعلة جامعة بينهما",
          "الاجتهاد الشخصي للعالم",
          "توافق آراء العلماء في مسألة"
        ],
        correctAnswer: "إلحاق فرع بأصل في الحكم لعلة جامعة بينهما",
      ),
      QuestionModel(
        question: "ما المقصود بمصطلح 'الإجماع' في أصول الفقه؟",
        options: [
          "اتفاق جميع المسلمين على حكم شرعي",
          "اتفاق المجتهدين من الأمة بعد وفاة النبي على حكم شرعي",
          "اتفاق أهل بلد معين على حكم شرعي",
          "اتفاق الصحابة فقط على حكم شرعي"
        ],
        correctAnswer: "اتفاق المجتهدين من الأمة بعد وفاة النبي على حكم شرعي",
      ),
      QuestionModel(
        question: "ما هي مصادر التشريع الإسلامي الأساسية؟",
        options: [
          "القرآن والسنة والإجماع والقياس",
          "القرآن والسنة والعادات والتقاليد",
          "القرآن والفتاوى والإجماع",
          "القياس والإجماع والعرف والمصلحة المرسلة"
        ],
        correctAnswer: "القرآن والسنة والإجماع والقياس",
      ),
      QuestionModel(
        question: "ما هو الفرق بين الفرض والواجب عند الحنفية؟",
        options: [
          "لا فرق بينهما",
          "الفرض ما ثبت بدليل قطعي والواجب ما ثبت بدليل ظني",
          "الفرض في العبادات والواجب في المعاملات",
          "الفرض ما كان مكتوبًا في القرآن والواجب ما جاء في السنة"
        ],
        correctAnswer: "الفرض ما ثبت بدليل قطعي والواجب ما ثبت بدليل ظني",
      ),
      QuestionModel(
        question: "متى يُعتبر الإجماع حجة في التشريع الإسلامي؟",
        options: [
          "إذا اتفق عليه جميع الصحابة فقط",
          "إذا اتفق عليه علماء عصر معين",
          "إذا اتفق عليه المجتهدون من الأمة في عصر من العصور",
          "إذا اتفق عليه أهل المدينة فقط"
        ],
        correctAnswer: "إذا اتفق عليه المجتهدون من الأمة في عصر من العصور",
      ),
      QuestionModel(
        question: "ما هو حكم الاجتهاد في الشريعة الإسلامية؟",
        options: [
          "فرض عين على كل مسلم",
          "فرض كفاية إذا قام به البعض سقط عن الباقين",
          "مستحب لمن توفرت فيه الشروط",
          "حرام إلا للضرورة"
        ],
        correctAnswer: "فرض كفاية إذا قام به البعض سقط عن الباقين",
      ),
      QuestionModel(
        question: "ما المقصود بالعلة في القياس؟",
        options: [
          "الحكم الشرعي",
          "الوصف الظاهر المنضبط المناسب للحكم",
          "المصلحة المترتبة على الحكم",
          "الدليل من القرآن والسنة"
        ],
        correctAnswer: "الوصف الظاهر المنضبط المناسب للحكم",
      ),
      QuestionModel(
        question: "ما هي المسألة التي اشتهر فيها خلاف الإمام أبي حنيفة مع جمهور الفقهاء في باب العبادات؟",
        options: [
          "مسح الرأس في الوضوء",
          "جواز بيع المعدوم",
          "رفع اليدين في الصلاة",
          "التيمم عند فقد الماء"
        ],
        correctAnswer: "مسح الرأس في الوضوء",
      ),
    ],
  ),

  // المستوى 6: الحديث الشريف
  LevelModel(
    level: 6,
    title: "الحديث الشريف",
    category: QuizCategory.islamic, // Assign category
    backgroundGradient: const [
      Color(0xFF3E2723),
      Color(0xFF4E342E),
      Color(0xFF5D4037),
      Color(0xFF6D4C41),
    ],
    questions: [
      QuestionModel(
        question: "ما هي السنة النبوية؟",
        options: [
          "القرآن الكريم",
          "ما أُثر عن النبي من قول أو فعل أو تقرير",
          "إجماع الصحابة",
          "أحكام الفقهاء"
        ],
        correctAnswer: "ما أُثر عن النبي من قول أو فعل أو تقرير",
      ),
      QuestionModel(
        question: "من هم الخلفاء الراشدون؟",
        options: [
          "أبو بكر وعمر وعثمان وعلي",
          "أبو بكر وعمر وخالد ومعاوية",
          "علي وعثمان وطلحة والزبير",
          "معاوية وعلي والحسن والحسين"
        ],
        correctAnswer: "أبو بكر وعمر وعثمان وعلي",
      ),
      QuestionModel(
        question: "من هو الصحابي الذي لُقب بأمين هذه الأمة؟",
        options: [
          "أبو هريرة رضي الله عنه",
          "أبو بكر الصديق رضي الله عنه",
          "أبو عبيدة بن الجراح رضي الله عنه",
          "عثمان بن عفان رضي الله عنه"
        ],
        correctAnswer: "أبو عبيدة بن الجراح رضي الله عنه",
      ),
      QuestionModel(
        question: "من هم العشرة المبشرون بالجنة؟",
        options: [
          "الخلفاء الراشدون وستة آخرون من الصحابة",
          "أهل بيت النبي كلهم",
          "أهل بدر كلهم",
          "أهل بيعة الرضوان كلهم"
        ],
        correctAnswer: "الخلفاء الراشدون وستة آخرون من الصحابة",
      ),
      QuestionModel(
        question: "ما هي الكتب الستة في الحديث؟",
        options: [
          "البخاري ومسلم وأبو داود والترمذي والنسائي وابن ماجه",
          "مسلم والبخاري ومالك وأحمد والنسائي والدارمي",
          "البخاري ومسلم والموطأ والمسند والكافي والوافي",
          "الموطأ والمسند والمصنف والسنن والصحاح والمستدرك"
        ],
        correctAnswer: "البخاري ومسلم وأبو داود والترمذي والنسائي وابن ماجه",
      ),
      QuestionModel(
        question: "ما هو الحديث الصحيح؟",
        options: [
          "ما رواه الصحابي عن النبي مباشرة",
          "ما اتصل سنده بنقل العدل الضابط عن مثله إلى منتهاه بلا شذوذ ولا علة",
          "ما رواه البخاري ومسلم فقط",
          "ما أجمع عليه العلماء"
        ],
        correctAnswer: "ما اتصل سنده بنقل العدل الضابط عن مثله إلى منتهاه بلا شذوذ ولا علة",
      ),
      QuestionModel(
        question: "مَن أول من دوّن الحديث النبوي بشكل رسمي؟",
        options: [
          "الإمام مالك",
          "محمد بن شهاب الزهري",
          "الإمام البخاري",
          "عمر بن عبد العزيز"
        ],
        correctAnswer: "محمد بن شهاب الزهري",
      ),
      QuestionModel(
        question: "ما هو الحديث القدسي؟",
        options: [
          "ما رواه الصحابي عن النبي",
          "ما أضافه النبي إلى ربه من كلام",
          "آيات القرآن الكريم",
          "ما أجمع عليه العلماء"
        ],
        correctAnswer: "ما أضافه النبي إلى ربه من كلام",
      ),
      QuestionModel(
        question: "من هو الصحابي الذي روى أكثر الأحاديث عن النبي صلى الله عليه وسلم؟",
        options: [
          "عبد الله بن عمر",
          "أنس بن مالك",
          "أبو هريرة",
          "عائشة أم المؤمنين"
        ],
        correctAnswer: "أبو هريرة",
      ),
      QuestionModel(
        question: "ما هي أصح كتب الحديث؟",
        options: [
          "صحيح البخاري وصحيح مسلم",
          "سنن أبي داود",
          "مسند الإمام أحمد",
          "موطأ الإمام مالك"
        ],
        correctAnswer: "صحيح البخاري وصحيح مسلم",
      ),
    ],
  ),

  // المستوى 7: التاريخ الإسلامي
  LevelModel(
    level: 7,
    title: "التاريخ الإسلامي",
    category: QuizCategory.islamic, // Assign category
    backgroundGradient: const [
      Color(0xFF0D47A1),
      Color(0xFF1565C0),
      Color(0xFF1976D2),
      Color(0xFF1E88E5),
    ],
    questions: [
      QuestionModel(
        question: "متى كانت غزوة بدر الكبرى؟",
        options: ["السنة الأولى للهجرة", "السنة الثانية للهجرة", "السنة الثالثة للهجرة", "السنة الرابعة للهجرة"],
        correctAnswer: "السنة الثانية للهجرة",
      ),
      QuestionModel(
        question: "من هو أول خليفة أموي؟",
        options: ["أبو بكر الصديق", "معاوية بن أبي سفيان", "عمر بن عبد العزيز", "عبد الملك بن مروان"],
        correctAnswer: "معاوية بن أبي سفيان",
      ),
      QuestionModel(
        question: "متى تم فتح الأندلس؟",
        options: ["92 هـ", "95 هـ", "98 هـ", "101 هـ"],
        correctAnswer: "92 هـ",
      ),
      QuestionModel(
        question: "من هو القائد المسلم الذي فتح مصر؟",
        options: ["خالد بن الوليد", "عمرو بن العاص", "سعد بن أبي وقاص", "طارق بن زياد"],
        correctAnswer: "عمرو بن العاص",
      ),
      QuestionModel(
        question: "من هو مؤسس الدولة العباسية؟",
        options: ["المعتصم بالله", "هارون الرشيد", "أبو العباس السفاح", "المأمون"],
        correctAnswer: "أبو العباس السفاح",
      ),
      QuestionModel(
        question: "من هو القائد المسلم الذي انتصر في معركة عين جالوت ضد المغول؟",
        options: ["صلاح الدين الأيوبي", "قطز", "الظاهر بيبرس", "سيف الدين قلاوون"],
        correctAnswer: "قطز",
      ),
      QuestionModel(
        question: "متى سقطت الخلافة العثمانية؟",
        options: ["1918 م", "1921 م", "1924 م", "1927 م"],
        correctAnswer: "1924 م",
      ),
      QuestionModel(
        question: "من هو الخليفة الذي لُقب بالفاروق؟",
        options: ["أبو بكر الصديق", "عمر بن الخطاب", "عثمان بن عفان", "علي بن أبي طالب"],
        correctAnswer: "عمر بن الخطاب",
      ),
      QuestionModel(
        question: "متى كانت معركة حطين بقيادة صلاح الدين الأيوبي؟",
        options: ["583 هـ", "587 هـ", "594 هـ", "598 هـ"],
        correctAnswer: "583 هـ",
      ),
      QuestionModel(
        question: "من هو الخليفة الذي لٌقب بذي النورين؟",
        options: ["أبو بكر الصديق", "عمر بن الخطاب", "عثمان بن عفان", "علي بن أبي طالب"],
        correctAnswer: "عثمان بن عفان",
      ),
    ],
  ),

  // المستوى 8: الشخصيات الإسلامية
  LevelModel(
    level: 8,
    title: "شخصيات إسلامية",
    category: QuizCategory.islamic, // Assign category
    backgroundGradient: const [
      Color(0xFF194D33),
      Color(0xFF2E7D32),
      Color(0xFF388E3C),
      Color(0xFF43A047),
    ],
    questions: [
      QuestionModel(
        question: "من هو الصحابي الذي لقب بسيف الله المسلول؟",
        options: ["خالد بن الوليد", "علي بن أبي طالب", "سعد بن أبي وقاص", "الزبير بن العوام"],
        correctAnswer: "خالد بن الوليد",
      ),
      QuestionModel(
        question: "من هو الإمام الذي لُقب بشيخ الإسلام وصاحب كتاب 'مجموع الفتاوى'؟",
        options: ["ابن باز", "ابن تيمية", "الشافعي", "ابن حنبل"],
        correctAnswer: "ابن تيمية",
      ),
      QuestionModel(
        question: "من هو العالم المسلم الذي اخترع الجبر؟",
        options: ["ابن الهيثم", "الخوارزمي", "ابن سينا", "البيروني"],
        correctAnswer: "الخوارزمي",
      ),
      QuestionModel(
        question: "من هو صاحب كتاب 'إحياء علوم الدين'؟",
        options: ["ابن القيم", "الإمام مالك", "الغزالي", "ابن حجر العسقلاني"],
        correctAnswer: "الغزالي",
      ),
      QuestionModel(
        question: "من هو القائد المسلم الذي حرر بيت المقدس من الصليبيين؟",
        options: ["صلاح الدين الأيوبي", "نور الدين زنكي", "عماد الدين زنكي", "سيف الدين قطز"],
        correctAnswer: "صلاح الدين الأيوبي",
      ),
      QuestionModel(
        question: "من صاحب كتاب 'البداية والنهاية'؟",
        options: ["ابن خلدون", "ابن كثير", "الطبري", "ابن الأثير"],
        correctAnswer: "ابن كثير",
      ),
      QuestionModel(
        question: "من هو مؤلف كتاب 'صحيح البخاري'؟",
        options: ["مسلم بن الحجاج", "محمد بن إسماعيل البخاري", "أبو داود السجستاني", "الترمذي"],
        correctAnswer: "محمد بن إسماعيل البخاري",
      ),
      QuestionModel(
        question: "من هو العالم المسلم الذي لُقب بالشيخ الرئيس وكان طبيباً وفيلسوفاً؟",
        options: ["ابن سينا", "ابن رشد", "الفارابي", "الرازي"],
        correctAnswer: "ابن سينا",
      ),
      QuestionModel(
        question: "من هو الصحابي الذي لقب بأسد الله وأسد رسوله؟",
        options: ["علي بن أبي طالب", "حمزة بن عبد المطلب", "خالد بن الوليد", "الزبير بن العوام"],
        correctAnswer: "حمزة بن عبد المطلب",
      ),
      QuestionModel(
        question: "من هو الخليفة الأموي الذي لُقب بخامس الخلفاء الراشدين؟",
        options: ["معاوية بن أبي سفيان", "عبد الملك بن مروان", "عمر بن عبد العزيز", "هشام بن عبد الملك"],
        correctAnswer: "عمر بن عبد العزيز",
      ),
    ],
  ),

  // المستوى 9: الآداب الإسلامية
  LevelModel(
    level: 9,
    title: "الآداب الإسلامية",
    category: QuizCategory.islamic, // Assign category
    backgroundGradient: const [
      Color(0xFFB71C1C),
      Color(0xFFC62828),
      Color(0xFFD32F2F),
      Color(0xFFE53935),
    ],
    questions: [
      QuestionModel(
        question: "ما هو الوقت المسنون للاستيقاظ في الإسلام؟",
        options: ["قبل الفجر", "بعد شروق الشمس", "عند الضحى", "قبل الظهر"],
        correctAnswer: "قبل الفجر",
      ),
      QuestionModel(
        question: "ما هو الذكر المسنون عند دخول المنزل؟",
        options: [
          "بسم الله والحمد لله",
          "بسم الله ولجنا وبسم الله خرجنا وعلى الله ربنا توكلنا",
          "اللهم إني أسألك خير المولج وخير المخرج",
          "اللهم بارك لنا في بيتنا"
        ],
        correctAnswer: "بسم الله ولجنا وبسم الله خرجنا وعلى الله ربنا توكلنا",
      ),
      QuestionModel(
        question: "ما هو حكم إفشاء السلام في الإسلام؟",
        options: ["فرض", "سنة", "واجب", "مستحب"],
        correctAnswer: "سنة",
      ),
      QuestionModel(
        question: "ما هو أدب المسلم عند العطاس؟",
        options: [
          "يضع يده على فمه ويقول الحمد لله",
          "يقول يرحمك الله",
          "يقول اللهم اشفني",
          "يقول أعوذ بالله من الشيطان الرجيم"
        ],
        correctAnswer: "يضع يده على فمه ويقول الحمد لله",
      ),
      QuestionModel(
        question: "ما هو الذكر المسنون عند النوم؟",
        options: [
          "باسمك اللهم أموت وأحيا",
          "الحمد لله الذي أحيانا بعد ما أماتنا وإليه النشور",
          "الحمد لله الذي عافاني في جسدي ورد علي روحي",
          "سبحانك اللهم وبحمدك"
        ],
        correctAnswer: "باسمك اللهم أموت وأحيا",
      ),
      QuestionModel(
        question: "ما هو أدب المسلم في المجالس؟",
        options: [
          "التوسع في الكلام والمزاح",
          "التكلف في اللباس والهيئة",
          "الإطالة في الجلوس",
          "عدم المقاطعة وحسن الاستماع"
        ],
        correctAnswer: "عدم المقاطعة وحسن الاستماع",
      ),
      QuestionModel(
        question: "ما هي آداب الطعام في الإسلام؟",
        options: [
          "الأكل باليد اليسرى",
          "التسمية قبل الأكل والأكل مما يليك",
          "التكلم أثناء الأكل",
          "ملء الصحن بالطعام"
        ],
        correctAnswer: "التسمية قبل الأكل والأكل مما يليك",
      ),
      QuestionModel(
        question: "ما هي آداب الزيارة في الإسلام؟",
        options: [
          "الإطالة في الزيارة",
          "الاستئذان قبل الدخول وعدم إطالة الجلوس",
          "الذهاب في أي وقت دون استئذان",
          "إخبار المضيف بالمجيء قبل دقائق"
        ],
        correctAnswer: "الاستئذان قبل الدخول وعدم إطالة الجلوس",
      ),
      QuestionModel(
        question: "ماذا يقول المسلم عند رؤية ما يعجبه؟",
        options: [
          "ما شاء الله",
          "سبحان الله",
          "الحمد لله",
          "الله أكبر"
        ],
        correctAnswer: "ما شاء الله",
      ),
      QuestionModel(
        question: "ما هو الأدب الإسلامي عند سماع أذان الصلاة؟",
        options: [
          "متابعة المؤذن في القول",
          "الاستمرار في الحديث حتى ينتهي الأذان",
          "ترديد آية الكرسي",
          "تلاوة سورة الفاتحة"
        ],
        correctAnswer: "متابعة المؤذن في القول",
      ),
    ],
  ),

  // المستوى 10: أعلام وحضارة
  LevelModel(
    level: 10,
    title: "أعلام وحضارة",
    category: QuizCategory.islamic, // Assign category
    backgroundGradient: const [
      Color(0xFF311B92),
      Color(0xFF4527A0),
      Color(0xFF512DA8),
      Color(0xFF5E35B1),
    ],
    questions: [
      QuestionModel(
        question: "من هو العالم المسلم الذي يُعتبر أول من اخترع الكاميرا (القمرة)؟",
        options: ["ابن الهيثم", "الخوارزمي", "ابن سينا", "البيروني"],
        correctAnswer: "ابن الهيثم",
      ),
      QuestionModel(
        question: "من الذي أسس علم الاجتماع؟",
        options: ["ابن خلدون", "الفارابي", "الغزالي", "ابن رشد"],
        correctAnswer: "ابن خلدون",
      ),
      QuestionModel(
        question: "في أي قرن كان العصر الذهبي للحضارة الإسلامية؟",
        options: [
          "القرن الثالث والرابع الهجري",
          "القرن السادس والسابع الهجري",
          "القرن التاسع والعاشر الهجري",
          "القرن الأول والثاني الهجري"
        ],
        correctAnswer: "القرن الثالث والرابع الهجري",
      ),
      QuestionModel(
        question: "من هو العالم المسلم المشهور بالطب والفلسفة ولقب بالشيخ الرئيس؟",
        options: ["ابن رشد", "الرازي", "ابن سينا", "ابن النفيس"],
        correctAnswer: "ابن سينا",
      ),
      QuestionModel(
        question: "أين تأسست أول جامعة في العالم؟",
        options: ["القاهرة (الأزهر)", "فاس (القرويين)", "بغداد (بيت الحكمة)", "قرطبة (جامعة قرطبة)"],
        correctAnswer: "فاس (القرويين)",
      ),
      QuestionModel(
        question: "من هو العالم المسلم الذي اكتشف الدورة الدموية الصغرى؟",
        options: ["ابن سينا", "الرازي", "ابن زهر", "ابن النفيس"],
        correctAnswer: "ابن النفيس",
      ),
      QuestionModel(
        question: "ما هو الاختراع العربي الذي انتقل إلى الغرب وكان سببا في النهضة العلمية الأوروبية؟",
        options: ["الورق", "البوصلة", "الأرقام العربية", "البارود"],
        correctAnswer: "الأرقام العربية",
      ),
      QuestionModel(
        question: "من هو مؤسس علم البصريات الحديث؟",
        options: ["الخوارزمي", "ابن الهيثم", "البيروني", "الرازي"],
        correctAnswer: "ابن الهيثم",
      ),
      QuestionModel(
        question: "من صاحب كتاب 'المناظر' في علم البصريات؟",
        options: ["ابن الهيثم", "الخوارزمي", "البيروني", "الكندي"],
        correctAnswer: "ابن الهيثم",
      ),
      QuestionModel(
        question: "ما هو المكان الذي أسس فيه المأمون مركزاً للترجمة؟",
        options: ["بيت الحكمة", "المدرسة المستنصرية", "الأزهر", "جامع قرطبة"],
        correctAnswer: "بيت الحكمة",
      ),
    ],
  ),

  // المستويات الإضافية الجديدة (11-20)
  LevelModel(
    level: 11,
    title: "الصحابة والتابعون",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF1A237E),
      Color(0xFF283593),
      Color(0xFF3949AB),
      Color(0xFF5C6BC0),
    ],
    questions: [
      QuestionModel(
        question: "ما هو اسم الصحابي الذي لقب بـ 'أسد الله الغالب'؟",
        options: ["خالد بن الوليد", "حمزة بن عبد المطلب", "علي بن أبي طالب", "عمر بن الخطاب"],
        correctAnswer: "حمزة بن عبد المطلب",
      ),
      QuestionModel(
        question: "في أي سورة ذكرت آية الكرسي؟",
        options: ["سورة البقرة", "سورة آل عمران", "سورة النساء", "سورة المائدة"],
        correctAnswer: "سورة البقرة",
      ),
      QuestionModel(
        question: "ما هو اسم زوجة فرعون المؤمنة؟",
        options: ["آسيا بنت مزاحم", "خديجة", "عائشة", "فاطمة"],
        correctAnswer: "آسيا بنت مزاحم",
      ),
      QuestionModel(
        question: "كم عدد أركان الوضوء؟",
        options: ["4", "5", "6", "7"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "ما هو اسم الغار الذي اختبأ فيه النبي وأبو بكر؟",
        options: ["غار حراء", "غار ثور", "غار النور", "غار الهدى"],
        correctAnswer: "غار ثور",
      ),
      QuestionModel(
        question: "ما هو اسم أول مسجد بني في الإسلام؟",
        options: ["المسجد الحرام", "المسجد النبوي", "مسجد قباء", "المسجد الأقصى"],
        correctAnswer: "مسجد قباء",
      ),
      QuestionModel(
        question: "من هو الصحابي الذي لقب بـ 'سيف الله المسلول'؟",
        options: ["خالد بن الوليد", "عمرو بن العاص", "سعد بن أبي وقاص", "أبو عبيدة بن الجراح"],
        correctAnswer: "خالد بن الوليد",
      ),
      QuestionModel(
        question: "ما هو اسم أم المؤمنين الأولى؟",
        options: ["عائشة بنت أبي بكر", "خديجة بنت خويلد", "حفصة بنت عمر", "زينب بنت جحش"],
        correctAnswer: "خديجة بنت خويلد",
      ),
      QuestionModel(
        question: "كم عدد الخلفاء الراشدين؟",
        options: ["3", "4", "5", "6"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "من هو آخر الخلفاء الراشدين؟",
        options: ["عثمان بن عفان", "علي بن أبي طالب", "عمر بن الخطاب", "أبو بكر الصديق"],
        correctAnswer: "علي بن أبي طالب",
      ),
    ],
  ),

  LevelModel(
    level: 12,
    title: "الفقه والأحكام",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF004D40),
      Color(0xFF00695C),
      Color(0xFF00796B),
      Color(0xFF00897B),
    ],
    questions: [
      QuestionModel(
        question: "ما هو اسم الصحابي الذي لقب بـ 'ذو النورين'؟",
        options: ["عثمان بن عفان", "علي بن أبي طالب", "عمر بن الخطاب", "أبو بكر الصديق"],
        correctAnswer: "عثمان بن عفان",
      ),
      QuestionModel(
        question: "كم عدد السجدات في القرآن الكريم؟",
        options: ["14", "15", "16", "17"],
        correctAnswer: "15",
      ),
      QuestionModel(
        question: "ما هو اسم أول شهيد في الإسلام؟",
        options: ["حمزة بن عبد المطلب", "سمية بنت خياط", "مصعب بن عمير", "عبد الله بن مسعود"],
        correctAnswer: "سمية بنت خياط",
      ),
      QuestionModel(
        question: "كم عدد أبواب الجنة؟",
        options: ["7", "8", "9", "10"],
        correctAnswer: "8",
      ),
      QuestionModel(
        question: "ما هو اسم الصحابي الذي لقب بـ 'الفاروق'؟",
        options: ["عمر بن الخطاب", "أبو بكر الصديق", "عثمان بن عفان", "علي بن أبي طالب"],
        correctAnswer: "عمر بن الخطاب",
      ),
      QuestionModel(
        question: "كم عدد أبواب النار؟",
        options: ["6", "7", "8", "9"],
        correctAnswer: "7",
      ),
      QuestionModel(
        question: "ما حكم صلاة الجماعة للرجال؟",
        options: ["فرض عين", "فرض كفاية", "سنة مؤكدة", "مستحب"],
        correctAnswer: "فرض كفاية",
      ),
      QuestionModel(
        question: "كم عدد التكبيرات في صلاة العيد؟",
        options: ["6 في الأولى و5 في الثانية", "7 في الأولى و5 في الثانية", "5 في الأولى و4 في الثانية", "4 في الأولى و3 في الثانية"],
        correctAnswer: "7 في الأولى و5 في الثانية",
      ),
      QuestionModel(
        question: "ما هو نصاب زكاة الذهب؟",
        options: ["20 مثقالاً", "85 جراماً", "200 درهم", "40 شاة"],
        correctAnswer: "85 جراماً",
      ),
      QuestionModel(
        question: "في أي عام هجري كانت غزوة بدر؟",
        options: ["السنة الأولى", "السنة الثانية", "السنة الثالثة", "السنة الرابعة"],
        correctAnswer: "السنة الثانية",
      ),
    ],
  ),

  // المستويات الإضافية (13-20)
  LevelModel(
    level: 13,
    title: "الأنبياء والرسل",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF7B1FA2),
      Color(0xFF8E24AA),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد الأنبياء المذكورين في القرآن الكريم؟",
        options: ["23", "24", "25", "26"],
        correctAnswer: "25",
      ),
      QuestionModel(
        question: "من هو النبي الذي ابتلعه الحوت؟",
        options: ["يونس عليه السلام", "موسى عليه السلام", "عيسى عليه السلام", "إبراهيم عليه السلام"],
        correctAnswer: "يونس عليه السلام",
      ),
      QuestionModel(
        question: "من هو النبي الذي كان يصنع الدروع؟",
        options: ["داود عليه السلام", "سليمان عليه السلام", "إدريس عليه السلام", "نوح عليه السلام"],
        correctAnswer: "داود عليه السلام",
      ),
      QuestionModel(
        question: "كم سنة دعا نوح عليه السلام قومه؟",
        options: ["900 سنة", "950 سنة", "1000 سنة", "850 سنة"],
        correctAnswer: "950 سنة",
      ),
      QuestionModel(
        question: "من هو النبي الذي تكلم مع الله مباشرة؟",
        options: ["موسى عليه السلام", "إبراهيم عليه السلام", "عيسى عليه السلام", "محمد صلى الله عليه وسلم"],
        correctAnswer: "موسى عليه السلام",
      ),
      QuestionModel(
        question: "من هو النبي الذي رفعه الله إلى السماء؟",
        options: ["عيسى عليه السلام", "إدريس عليه السلام", "إلياس عليه السلام", "يحيى عليه السلام"],
        correctAnswer: "عيسى عليه السلام",
      ),
      QuestionModel(
        question: "من هو النبي الذي أوتي الزبور؟",
        options: ["داود عليه السلام", "سليمان عليه السلام", "موسى عليه السلام", "عيسى عليه السلام"],
        correctAnswer: "داود عليه السلام",
      ),
      QuestionModel(
        question: "من هو النبي الذي سخر الله له الريح والجن؟",
        options: ["سليمان عليه السلام", "داود عليه السلام", "موسى عليه السلام", "يوسف عليه السلام"],
        correctAnswer: "سليمان عليه السلام",
      ),
      QuestionModel(
        question: "من هو النبي الذي ولد بدون أب؟",
        options: ["عيسى عليه السلام", "آدم عليه السلام", "يحيى عليه السلام", "إسحاق عليه السلام"],
        correctAnswer: "عيسى عليه السلام",
      ),
      QuestionModel(
        question: "من هو النبي الذي كان يفهم لغة الطيور والحيوانات؟",
        options: ["سليمان عليه السلام", "داود عليه السلام", "يوسف عليه السلام", "موسى عليه السلام"],
        correctAnswer: "سليمان عليه السلام",
      ),
    ],
  ),

  LevelModel(
    level: 14,
    title: "الملائكة والجن",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF1565C0),
      Color(0xFF1976D2),
      Color(0xFF1E88E5),
      Color(0xFF2196F3),
    ],
    questions: [
      QuestionModel(
        question: "من هو الملك الموكل بالوحي؟",
        options: ["جبريل عليه السلام", "ميكائيل عليه السلام", "إسرافيل عليه السلام", "عزرائيل عليه السلام"],
        correctAnswer: "جبريل عليه السلام",
      ),
      QuestionModel(
        question: "من هو الملك الموكل بالمطر والنبات؟",
        options: ["ميكائيل عليه السلام", "جبريل عليه السلام", "إسرافيل عليه السلام", "عزرائيل عليه السلام"],
        correctAnswer: "ميكائيل عليه السلام",
      ),
      QuestionModel(
        question: "من هو الملك الموكل بالنفخ في الصور؟",
        options: ["إسرافيل عليه السلام", "جبريل عليه السلام", "ميكائيل عليه السلام", "عزرائيل عليه السلام"],
        correctAnswer: "إسرافيل عليه السلام",
      ),
      QuestionModel(
        question: "من هو الملك الموكل بقبض الأرواح؟",
        options: ["عزرائيل عليه السلام", "جبريل عليه السلام", "ميكائيل عليه السلام", "إسرافيل عليه السلام"],
        correctAnswer: "عزرائيل عليه السلام",
      ),
      QuestionModel(
        question: "ما هو اسم خازن الجنة؟",
        options: ["رضوان", "مالك", "منكر", "نكير"],
        correctAnswer: "رضوان",
      ),
      QuestionModel(
        question: "ما هو اسم خازن النار؟",
        options: ["مالك", "رضوان", "منكر", "نكير"],
        correctAnswer: "مالك",
      ),
      QuestionModel(
        question: "ما هما اسما الملكين اللذين يسألان في القبر؟",
        options: ["منكر ونكير", "هاروت وماروت", "رقيب وعتيد", "كرام كاتبين"],
        correctAnswer: "منكر ونكير",
      ),
      QuestionModel(
        question: "مم خلقت الملائكة؟",
        options: ["من نور", "من نار", "من طين", "من ماء"],
        correctAnswer: "من نور",
      ),
      QuestionModel(
        question: "مم خلق الجن؟",
        options: ["من نار", "من نور", "من طين", "من ماء"],
        correctAnswer: "من نار",
      ),
      QuestionModel(
        question: "ما اسم أبو الجن؟",
        options: ["إبليس", "الشيطان", "الجان", "مارد"],
        correctAnswer: "الجان",
      ),
    ],
  ),

  LevelModel(
    level: 15,
    title: "اليوم الآخر",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF2E7D32),
      Color(0xFF388E3C),
      Color(0xFF43A047),
      Color(0xFF4CAF50),
    ],
    questions: [
      QuestionModel(
        question: "كم مرة ينفخ في الصور؟",
        options: ["مرة واحدة", "مرتان", "ثلاث مرات", "أربع مرات"],
        correctAnswer: "مرتان",
      ),
      QuestionModel(
        question: "ما هو اسم الصراط الذي يمر عليه الناس يوم القيامة؟",
        options: ["الصراط المستقيم", "صراط الآخرة", "صراط الحساب", "صراط النار"],
        correctAnswer: "الصراط المستقيم",
      ),
      QuestionModel(
        question: "كم يوم يقدر يوم القيامة؟",
        options: ["ألف سنة", "خمسين ألف سنة", "مائة ألف سنة", "لا يعلم إلا الله"],
        correctAnswer: "خمسين ألف سنة",
      ),
      QuestionModel(
        question: "ما هو اسم الميزان الذي توزن به الأعمال؟",
        options: ["الميزان", "المقياس", "الحساب", "العدل"],
        correctAnswer: "الميزان",
      ),
      QuestionModel(
        question: "كم درجة في الجنة؟",
        options: ["سبع درجات", "مائة درجة", "ألف درجة", "لا تحصى"],
        correctAnswer: "مائة درجة",
      ),
      QuestionModel(
        question: "ما هو اسم أعلى مكان في الجنة؟",
        options: ["الفردوس الأعلى", "جنة عدن", "جنة النعيم", "جنة المأوى"],
        correctAnswer: "الفردوس الأعلى",
      ),
      QuestionModel(
        question: "كم درك في النار؟",
        options: ["سبعة أدراك", "عشرة أدراك", "مائة درك", "لا تحصى"],
        correctAnswer: "سبعة أدراك",
      ),
      QuestionModel(
        question: "ما هو اسم أسفل مكان في النار؟",
        options: ["الهاوية", "جهنم", "السعير", "الحطمة"],
        correctAnswer: "الهاوية",
      ),
      QuestionModel(
        question: "من هم أول من يدخل الجنة؟",
        options: ["الأنبياء", "الشهداء", "الصديقون", "الفقراء"],
        correctAnswer: "الفقراء",
      ),
      QuestionModel(
        question: "ما هو الحوض المورود؟",
        options: ["حوض النبي محمد صلى الله عليه وسلم", "نهر في الجنة", "بحيرة في الآخرة", "مكان الحساب"],
        correctAnswer: "حوض النبي محمد صلى الله عليه وسلم",
      ),
    ],
  ),

  LevelModel(
    level: 16,
    title: "الحج والعمرة",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFFD32F2F),
      Color(0xFFE53935),
      Color(0xFFF44336),
      Color(0xFFEF5350),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد أركان الحج؟",
        options: ["3", "4", "5", "6"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "ما هو الركن الأول من أركان الحج؟",
        options: ["الإحرام", "الوقوف بعرفة", "طواف الإفاضة", "السعي"],
        correctAnswer: "الإحرام",
      ),
      QuestionModel(
        question: "في أي يوم يقف الحجاج بعرفة؟",
        options: ["8 ذو الحجة", "9 ذو الحجة", "10 ذو الحجة", "11 ذو الحجة"],
        correctAnswer: "9 ذو الحجة",
      ),
      QuestionModel(
        question: "كم عدد أشواط الطواف؟",
        options: ["6", "7", "8", "9"],
        correctAnswer: "7",
      ),
      QuestionModel(
        question: "كم عدد أشواط السعي؟",
        options: ["6", "7", "8", "9"],
        correctAnswer: "7",
      ),
      QuestionModel(
        question: "ما هو اسم الجبل الذي يبدأ منه السعي؟",
        options: ["الصفا", "المروة", "عرفة", "المزدلفة"],
        correctAnswer: "الصفا",
      ),
      QuestionModel(
        question: "كم مرة يرمي الحاج الجمرات في أيام التشريق؟",
        options: ["مرة واحدة", "مرتان", "ثلاث مرات", "أربع مرات"],
        correctAnswer: "ثلاث مرات",
      ),
      QuestionModel(
        question: "ما هو اسم الثوب الذي يلبسه الرجل في الإحرام؟",
        options: ["الإزار والرداء", "الثوب الأبيض", "الجلباب", "القميص"],
        correctAnswer: "الإزار والرداء",
      ),
      QuestionModel(
        question: "في أي شهر يؤدى الحج؟",
        options: ["ذو القعدة", "ذو الحجة", "محرم", "صفر"],
        correctAnswer: "ذو الحجة",
      ),
      QuestionModel(
        question: "ما هو الفرق بين الحج والعمرة؟",
        options: ["الحج له وقت محدد والعمرة في أي وقت", "لا فرق بينهما", "العمرة أطول من الحج", "الحج للرجال والعمرة للنساء"],
        correctAnswer: "الحج له وقت محدد والعمرة في أي وقت",
      ),
    ],
  ),

  LevelModel(
    level: 17,
    title: "الأخلاق الإسلامية",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF7B1FA2),
      Color(0xFF8E24AA),
      Color(0xFF9C27B0),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "ما هو أفضل الأخلاق في الإسلام؟",
        options: ["الصدق", "الأمانة", "حسن الخلق", "العدل"],
        correctAnswer: "حسن الخلق",
      ),
      QuestionModel(
        question: "ما هو ضد الكبر؟",
        options: ["التواضع", "الخشوع", "الصبر", "الحلم"],
        correctAnswer: "التواضع",
      ),
      QuestionModel(
        question: "ما هو أعظم الذنوب عند الله؟",
        options: ["الشرك بالله", "القتل", "الزنا", "السرقة"],
        correctAnswer: "الشرك بالله",
      ),
      QuestionModel(
        question: "ما هو جزاء البر بالوالدين؟",
        options: ["الجنة", "المغفرة", "البركة", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "ما هو حكم الكذب في الإسلام؟",
        options: ["حرام", "مكروه", "جائز أحياناً", "مستحب"],
        correctAnswer: "حرام",
      ),
      QuestionModel(
        question: "ما هو أجر من كظم غيظه؟",
        options: ["الأجر العظيم", "المغفرة", "دخول الجنة", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "ما هو حكم الغيبة في الإسلام؟",
        options: ["حرام", "مكروه", "جائز", "مستحب"],
        correctAnswer: "حرام",
      ),
      QuestionModel(
        question: "ما هو أفضل الجهاد؟",
        options: ["جهاد النفس", "الجهاد في سبيل الله", "جهاد الكلمة", "جميع ما سبق"],
        correctAnswer: "جهاد النفس",
      ),
      QuestionModel(
        question: "ما هو حكم الإحسان إلى الجار؟",
        options: ["واجب", "مستحب", "مباح", "مكروه"],
        correctAnswer: "واجب",
      ),
      QuestionModel(
        question: "ما هو جزاء من عفا عن الناس؟",
        options: ["يعفو الله عنه", "يدخل الجنة", "يحبه الله", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
    ],
  ),

  LevelModel(
    level: 18,
    title: "التاريخ الإسلامي",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF5D4037),
      Color(0xFF6D4C41),
      Color(0xFF795548),
      Color(0xFF8D6E63),
    ],
    questions: [
      QuestionModel(
        question: "في أي عام هجري فتحت مكة؟",
        options: ["7 هـ", "8 هـ", "9 هـ", "10 هـ"],
        correctAnswer: "8 هـ",
      ),
      QuestionModel(
        question: "كم سنة استمرت الخلافة الراشدة؟",
        options: ["25 سنة", "30 سنة", "35 سنة", "40 سنة"],
        correctAnswer: "30 سنة",
      ),
      QuestionModel(
        question: "من هو مؤسس الدولة الأموية؟",
        options: ["معاوية بن أبي سفيان", "عبد الملك بن مروان", "الوليد بن عبد الملك", "هشام بن عبد الملك"],
        correctAnswer: "معاوية بن أبي سفيان",
      ),
      QuestionModel(
        question: "في أي مدينة كانت عاصمة الدولة العباسية؟",
        options: ["دمشق", "بغداد", "القاهرة", "قرطبة"],
        correctAnswer: "بغداد",
      ),
      QuestionModel(
        question: "من هو القائد الذي فتح الأندلس؟",
        options: ["طارق بن زياد", "موسى بن نصير", "عقبة بن نافع", "عمرو بن العاص"],
        correctAnswer: "طارق بن زياد",
      ),
      QuestionModel(
        question: "في أي معركة انتصر المسلمون على الصليبيين بقيادة صلاح الدين؟",
        options: ["معركة حطين", "معركة عين جالوت", "معركة اليرموك", "معركة القادسية"],
        correctAnswer: "معركة حطين",
      ),
      QuestionModel(
        question: "من هو القائد الذي أوقف زحف المغول؟",
        options: ["سيف الدين قطز", "صلاح الدين الأيوبي", "نور الدين زنكي", "الظاهر بيبرس"],
        correctAnswer: "سيف الدين قطز",
      ),
      QuestionModel(
        question: "كم سنة حكمت الدولة العثمانية؟",
        options: ["500 سنة", "600 سنة", "700 سنة", "800 سنة"],
        correctAnswer: "600 سنة",
      ),
      QuestionModel(
        question: "من هو السلطان العثماني الذي فتح القسطنطينية؟",
        options: ["محمد الفاتح", "سليمان القانوني", "بايزيد الأول", "مراد الثاني"],
        correctAnswer: "محمد الفاتح",
      ),
      QuestionModel(
        question: "في أي قرن سقطت الخلافة العثمانية؟",
        options: ["القرن 18", "القرن 19", "القرن 20", "القرن 21"],
        correctAnswer: "القرن 20",
      ),
    ],
  ),

  LevelModel(
    level: 19,
    title: "العلوم الإسلامية",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF0277BD),
      Color(0xFF0288D1),
      Color(0xFF039BE5),
      Color(0xFF03A9F4),
    ],
    questions: [
      QuestionModel(
        question: "ما هو علم التفسير؟",
        options: ["علم شرح القرآن", "علم الحديث", "علم الفقه", "علم العقيدة"],
        correctAnswer: "علم شرح القرآن",
      ),
      QuestionModel(
        question: "من هو إمام المفسرين؟",
        options: ["ابن كثير", "الطبري", "القرطبي", "البغوي"],
        correctAnswer: "الطبري",
      ),
      QuestionModel(
        question: "ما هو علم الحديث؟",
        options: ["علم دراسة أقوال وأفعال النبي", "علم التفسير", "علم الفقه", "علم العقيدة"],
        correctAnswer: "علم دراسة أقوال وأفعال النبي",
      ),
      QuestionModel(
        question: "من هو إمام المحدثين؟",
        options: ["البخاري", "مسلم", "أبو داود", "الترمذي"],
        correctAnswer: "البخاري",
      ),
      QuestionModel(
        question: "ما هو علم الفقه؟",
        options: ["علم الأحكام الشرعية", "علم التفسير", "علم الحديث", "علم العقيدة"],
        correctAnswer: "علم الأحكام الشرعية",
      ),
      QuestionModel(
        question: "من هو إمام الفقهاء؟",
        options: ["أبو حنيفة", "مالك", "الشافعي", "أحمد بن حنبل"],
        correctAnswer: "أبو حنيفة",
      ),
      QuestionModel(
        question: "كم مذهب فقهي معتبر في الإسلام؟",
        options: ["3", "4", "5", "6"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "ما هو علم العقيدة؟",
        options: ["علم الإيمان والتوحيد", "علم الفقه", "علم التفسير", "علم الحديث"],
        correctAnswer: "علم الإيمان والتوحيد",
      ),
      QuestionModel(
        question: "ما هو علم أصول الفقه؟",
        options: ["علم استنباط الأحكام", "علم التفسير", "علم الحديث", "علم العقيدة"],
        correctAnswer: "علم استنباط الأحكام",
      ),
      QuestionModel(
        question: "من هو واضع علم أصول الفقه؟",
        options: ["الإمام الشافعي", "الإمام أبو حنيفة", "الإمام مالك", "الإمام أحمد"],
        correctAnswer: "الإمام الشافعي",
      ),
    ],
  ),

  LevelModel(
    level: 20,
    title: "الإعجاز في الإسلام",
    category: QuizCategory.islamic,
    backgroundGradient: const [
      Color(0xFF388E3C),
      Color(0xFF43A047),
      Color(0xFF4CAF50),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "ما هو الإعجاز العلمي في القرآن؟",
        options: ["ذكر حقائق علمية قبل اكتشافها", "البلاغة والفصاحة", "القصص والأمثال", "الأحكام والتشريعات"],
        correctAnswer: "ذكر حقائق علمية قبل اكتشافها",
      ),
      QuestionModel(
        question: "في أي آية ذكر أن الأرض كروية؟",
        options: ["يكور الليل على النهار", "والأرض بعد ذلك دحاها", "والأرض مددناها", "والأرض وضعها للأنام"],
        correctAnswer: "يكور الليل على النهار",
      ),
      QuestionModel(
        question: "ما هو الإعجاز البلاغي في القرآن؟",
        options: ["الفصاحة والبيان", "الحقائق العلمية", "القصص", "الأحكام"],
        correctAnswer: "الفصاحة والبيان",
      ),
      QuestionModel(
        question: "كم عدد أوجه الإعجاز في القرآن؟",
        options: ["3", "5", "7", "متعددة"],
        correctAnswer: "متعددة",
      ),
      QuestionModel(
        question: "ما هو الإعجاز التشريعي في الإسلام؟",
        options: ["عدالة الأحكام وشموليتها", "البلاغة", "العلم", "التاريخ"],
        correctAnswer: "عدالة الأحكام وشموليتها",
      ),
      QuestionModel(
        question: "في أي آية ذكر أن كل شيء خلق من الماء؟",
        options: ["وجعلنا من الماء كل شيء حي", "والله خلق كل دابة من ماء", "وهو الذي خلق من الماء بشراً", "ونزلنا من السماء ماء"],
        correctAnswer: "وجعلنا من الماء كل شيء حي",
      ),
      QuestionModel(
        question: "ما هو الإعجاز الغيبي في القرآن؟",
        options: ["الإخبار عن المستقبل", "البلاغة", "العلم", "التشريع"],
        correctAnswer: "الإخبار عن المستقبل",
      ),
      QuestionModel(
        question: "في أي سورة ذكرت نبوءة انتصار الروم؟",
        options: ["سورة الروم", "سورة البقرة", "سورة آل عمران", "سورة النساء"],
        correctAnswer: "سورة الروم",
      ),
      QuestionModel(
        question: "ما هو الإعجاز العددي في القرآن؟",
        options: ["التناسق في الأرقام والأعداد", "البلاغة", "العلم", "التشريع"],
        correctAnswer: "التناسق في الأرقام والأعداد",
      ),
      QuestionModel(
        question: "لماذا عجز العرب عن الإتيان بمثل القرآن؟",
        options: ["لأنه كلام الله المعجز", "لأنه صعب", "لأنه طويل", "لأنه قديم"],
        correctAnswer: "لأنه كلام الله المعجز",
      ),
    ],
  ),
];

/// --- 2. قائمة مستويات الرياضيات ---
final List<LevelModel> mathLevels = [
  // المستوى 1: العمليات الحسابية الأساسية
  LevelModel(
    level: 1,
    title: "العمليات الأساسية",
    category: QuizCategory.math,
    isUnlocked: true,
    backgroundGradient: const [
      Color(0xFF4CAF50),
      Color(0xFF8BC34A),
      Color(0xFFCDDC39),
      Color(0xFFF0F4C3),
    ],
    questions: [
      QuestionModel(
        question: "5 + 3 = ?",
        options: ["7", "8", "9", "10"],
        correctAnswer: "8",
      ),
      QuestionModel(
        question: "9 - 4 = ?",
        options: ["3", "4", "5", "6"],
        correctAnswer: "5",
      ),
      QuestionModel(
        question: "3 × 2 = ?",
        options: ["5", "6", "7", "8"],
        correctAnswer: "6",
      ),
      QuestionModel(
        question: "8 ÷ 2 = ?",
        options: ["2", "3", "4", "5"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "10 + 15 = ?",
        options: ["23", "25", "27", "30"],
        correctAnswer: "25",
      ),
      QuestionModel(
        question: "12 - 7 = ?",
        options: ["3", "4", "5", "6"],
        correctAnswer: "5",
      ),
      QuestionModel(
        question: "4 × 4 = ?",
        options: ["12", "14", "16", "18"],
        correctAnswer: "16",
      ),
      QuestionModel(
        question: "20 ÷ 4 = ?",
        options: ["4", "5", "6", "7"],
        correctAnswer: "5",
      ),
      QuestionModel(
        question: "6 + 9 = ?",
        options: ["13", "14", "15", "16"],
        correctAnswer: "15",
      ),
      QuestionModel(
        question: "18 - 9 = ?",
        options: ["8", "9", "10", "11"],
        correctAnswer: "9",
      ),
    ],
  ),
  
  // المستوى 2: عمليات حسابية أكثر تعقيدًا
  LevelModel(
    level: 2,
    title: "حسابات متوسطة",
    category: QuizCategory.math,
    backgroundGradient: const [
      Color(0xFF4CAF50),
      Color(0xFF8BC34A),
      Color(0xFFCDDC39),
      Color(0xFFF0F4C3),
    ],
    questions: [
      QuestionModel(
        question: "15 + 27 = ?",
        options: ["32", "39", "42", "45"],
        correctAnswer: "42",
      ),
      QuestionModel(
        question: "36 - 19 = ?",
        options: ["15", "16", "17", "18"],
        correctAnswer: "17",
      ),
      QuestionModel(
        question: "7 × 6 = ?",
        options: ["36", "42", "48", "54"],
        correctAnswer: "42",
      ),
      QuestionModel(
        question: "32 ÷ 8 = ?",
        options: ["3", "4", "5", "6"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "48 - 29 = ?",
        options: ["17", "18", "19", "20"],
        correctAnswer: "19",
      ),
      QuestionModel(
        question: "8 × 9 = ?",
        options: ["63", "68", "72", "81"],
        correctAnswer: "72",
      ),
      QuestionModel(
        question: "54 ÷ 9 = ?",
        options: ["5", "6", "7", "8"],
        correctAnswer: "6",
      ),
      QuestionModel(
        question: "23 + 38 = ?",
        options: ["51", "61", "71", "81"],
        correctAnswer: "61",
      ),
      QuestionModel(
        question: "45 - 28 = ?",
        options: ["15", "17", "19", "21"],
        correctAnswer: "17",
      ),
      QuestionModel(
        question: "7 × 7 = ?",
        options: ["47", "49", "51", "56"],
        correctAnswer: "49",
      ),
    ],
  ),
  
  // المستوى 3: عمليات مختلطة
  LevelModel(
    level: 3,
    title: "عمليات مختلطة",
    category: QuizCategory.math,
    backgroundGradient: const [
      Color(0xFF4CAF50),
      Color(0xFF8BC34A),
      Color(0xFFCDDC39),
      Color(0xFFF0F4C3),
    ],
    questions: [
      QuestionModel(
        question: "(4 + 3) × 2 = ?",
        options: ["10", "12", "14", "16"],
        correctAnswer: "14",
      ),
      QuestionModel(
        question: "18 ÷ (6 - 3) = ?",
        options: ["4", "6", "8", "9"],
        correctAnswer: "6",
      ),
      QuestionModel(
        question: "5 × 4 + 10 = ?",
        options: ["20", "25", "30", "35"],
        correctAnswer: "30",
      ),
      QuestionModel(
        question: "30 - 5 × 3 = ?",
        options: ["15", "20", "25", "75"],
        correctAnswer: "15",
      ),
      QuestionModel(
        question: "8 + 16 ÷ 4 = ?",
        options: ["4", "8", "12", "16"],
        correctAnswer: "12",
      ),
      QuestionModel(
        question: "9 × (2 + 3) = ?",
        options: ["35", "40", "45", "50"],
        correctAnswer: "45",
      ),
      QuestionModel(
        question: "24 ÷ 6 + 8 = ?",
        options: ["12", "16", "24", "32"],
        correctAnswer: "12",
      ),
      QuestionModel(
        question: "7 + 3 × 5 = ?",
        options: ["22", "50", "25", "20"],
        correctAnswer: "22",
      ),
      QuestionModel(
        question: "(20 - 12) × 3 = ?",
        options: ["12", "18", "24", "27"],
        correctAnswer: "24",
      ),
      QuestionModel(
        question: "36 ÷ (2 × 3) = ?",
        options: ["4", "6", "8", "12"],
        correctAnswer: "6",
      ),
    ],
  ),
  
  // باقي مستويات الرياضيات يمكن إضافتها بنفس الطريقة
];

/// --- 3. قائمة المستويات التاريخية مع أسئلة مترابطة للموضوعات ---
final List<LevelModel> historicalLevels = [
  // المستوى 1: أحداث تاريخية عامة (أساسيات تاريخية بسيطة)
  LevelModel(
    level: 1,
    title: "أحداث تاريخية عامة",
    category: QuizCategory.historical,
    isUnlocked: true,
    backgroundGradient: const [
      Color(0xFFBF360C),
      Color(0xFFD84315),
      Color(0xFFE64A19),
      Color(0xFFF57C00),
    ],
    questions: [
      QuestionModel(
        question: "في أي قارة تقع مصر القديمة؟",
        options: ["آسيا", "أوروبا", "أفريقيا", "أمريكا الجنوبية"],
        correctAnswer: "أفريقيا",
      ),
      QuestionModel(
        question: "ما هي الحضارة التي بنت الأهرامات في الجيزة؟",
        options: ["الرومانية", "اليونانية", "المصرية القديمة", "السومرية"],
        correctAnswer: "المصرية القديمة",
      ),
      QuestionModel(
        question: "من هو أول خليفة راشدي بعد النبي محمد صلى الله عليه وسلم؟",
        options: ["عمر بن الخطاب", "علي بن أبي طالب", "عثمان بن عفان", "أبو بكر الصديق"],
        correctAnswer: "أبو بكر الصديق",
      ),
      QuestionModel(
        question: "ما هي المدينة التي كانت عاصمة الخلافة الأموية؟",
        options: ["بغداد", "القاهرة", "دمشق", "قرطبة"],
        correctAnswer: "دمشق",
      ),
      QuestionModel(
        question: "متى بدأت الحرب العالمية الأولى؟",
        options: ["1905م", "1914م", "1918م", "1920م"],
        correctAnswer: "1914م",
      ),
      QuestionModel(
        question: "من هو المستكشف الذي يُنسب إليه اكتشاف أمريكا؟",
        options: ["فاسكو دا جاما", "كريستوفر كولومبوس", "فرديناند ماجلان", "جيمس كوك"],
        correctAnswer: "كريستوفر كولومبوس",
      ),
      QuestionModel(
        question: "متى انتهت الحرب العالمية الثانية؟",
        options: ["1943م", "1945م", "1947م", "1950م"],
        correctAnswer: "1945م",
      ),
      QuestionModel(
        question: "ما هي أطول حرب في التاريخ الأوروبي الحديث؟",
        options: ["حرب المائة عام", "الحرب العالمية الأولى", "حرب الثلاثين عامًا", "الحروب النابليونية"],
        correctAnswer: "حرب المائة عام",
      ),
      QuestionModel(
        question: "في أي قرن بدأت الثورة الصناعية؟",
        options: ["القرن السادس عشر", "القرن السابع عشر", "القرن الثامن عشر", "القرن التاسع عشر"],
        correctAnswer: "القرن الثامن عشر",
      ),
      QuestionModel(
        question: "من هو القائد الذي فتح القسطنطينية عام 1453م؟",
        options: ["صلاح الدين الأيوبي", "محمد الفاتح", "سليمان القانوني", "طارق بن زياد"],
        correctAnswer: "محمد الفاتح",
      ),
    ],
  ),
  
  // المستوى 2: أحداث تاريخية متقدمة (أحداث أكثر تخصصاً في التاريخ)
  LevelModel(
    level: 2,
    title: "أحداث تاريخية متقدمة",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFFBF360C),
      Color(0xFFD84315),
      Color(0xFFE64A19),
      Color(0xFFF57C00),
    ],
    questions: [
      QuestionModel(
        question: "من هو القائد الذي وحّد معظم إنجلترا تحت حكم واحد في القرن العاشر؟",
        options: ["الملك آرثر", "وليام الفاتح", "ألفريد العظيم", "هنري الثامن"],
        correctAnswer: "ألفريد العظيم",
      ),
      QuestionModel(
        question: "ما هي المعركة التي أنهت الوجود الصليبي في الشرق الأوسط؟",
        options: ["معركة حطين", "معركة عين جالوت", "فتح القسطنطينية", "فتح عكا"],
        correctAnswer: "فتح عكا",
      ),
      QuestionModel(
        question: "ما هي السلالة التي حكمت الصين خلال عصر الاكتشافات البحرية في القرن الخامس عشر؟",
        options: ["سلالة مينغ", "سلالة تشينغ", "سلالة هان", "سلالة سونغ"],
        correctAnswer: "سلالة مينغ",
      ),
      QuestionModel(
        question: "ما هي الدولة التي حكمت البرازيل قبل استقلالها؟",
        options: ["إسبانيا", "فرنسا", "البرتغال", "هولندا"],
        correctAnswer: "البرتغال",
      ),
      QuestionModel(
        question: "متى وقعت معركة واترلو الشهيرة؟",
        options: ["1805م", "1815م", "1825م", "1835م"],
        correctAnswer: "1815م",
      ),
      QuestionModel(
        question: "من هو الإمبراطور الذي أصدر مرسوم ميلانو الذي منح الحرية الدينية في الإمبراطورية الرومانية؟",
        options: ["نيرون", "أغسطس", "قسطنطين الأول", "تراجان"],
        correctAnswer: "قسطنطين الأول",
      ),
      QuestionModel(
        question: "متى تم افتتاح قناة السويس؟",
        options: ["1859م", "1869م", "1879م", "1889م"],
        correctAnswer: "1869م",
      ),
      QuestionModel(
        question: "من هو قائد الثورة الفرنسية الذي عُرف بحكم الإرهاب؟",
        options: ["نابليون بونابرت", "لويس السادس عشر", "روبسبير", "دانتون"],
        correctAnswer: "روبسبير",
      ),
      QuestionModel(
        question: "ما هي أول دولة في العالم استخدمت النظام العشري للعملة؟",
        options: ["بريطانيا", "فرنسا", "الولايات المتحدة", "روسيا"],
        correctAnswer: "فرنسا",
      ),
      QuestionModel(
        question: "من هو المستكشف البرتغالي الذي وصل إلى الهند عبر رأس الرجاء الصالح؟",
        options: ["كريستوفر كولومبوس", "فرديناند ماجلان", "فاسكو دا جاما", "أميريغو فسبوتشي"],
        correctAnswer: "فاسكو دا جاما",
      ),
    ],
  ),
  
  // المستوى 3: الحضارات القديمة
  LevelModel(
    level: 3,
    title: "الحضارات القديمة",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFFBF360C),
      Color(0xFFD84315),
      Color(0xFFE64A19),
      Color(0xFFF57C00),
    ],
    questions: [
      QuestionModel(
        question: "ما هي أقدم حضارة مكتشفة في العالم؟",
        options: ["الحضارة المصرية", "حضارة بلاد الرافدين (سومر)", "الحضارة الصينية", "حضارة وادي السند"],
        correctAnswer: "حضارة بلاد الرافدين (سومر)",
      ),
      QuestionModel(
        question: "ما هي الكتابة التي استخدمها المصريون القدماء؟",
        options: ["الكتابة المسمارية", "الهيروغليفية", "الأبجدية الفينيقية", "الكتابة اليونانية"],
        correctAnswer: "الهيروغليفية",
      ),
      QuestionModel(
        question: "أين نشأت الحضارة السومرية؟",
        options: ["بلاد الشام", "وادي النيل", "بلاد الرافدين", "شبه الجزيرة العربية"],
        correctAnswer: "بلاد الرافدين",
      ),
      QuestionModel(
        question: "ما اسم ملك بابل الذي اشتهر بشريعته وقوانينه المكتوبة؟",
        options: ["نبوخذ نصر", "حمورابي", "جلجامش", "سرجون الأكدي"],
        correctAnswer: "حمورابي",
      ),
      QuestionModel(
        question: "من بنى المدرجات الزراعية المعروفة في حضارة أمريكا الجنوبية؟",
        options: ["المايا", "الإنكا", "الأزتك", "الأولمك"],
        correctAnswer: "الإنكا",
      ),
      QuestionModel(
        question: "ما هي الحضارة التي بنت مدينة البتراء؟",
        options: ["الرومانية", "الأنباط", "الكلدانيين", "الفراعنة"],
        correctAnswer: "الأنباط",
      ),
      QuestionModel(
        question: "ما هي أشهر المعابد في الحضارة المصرية القديمة؟",
        options: ["معبد الكرنك", "البانثيون", "معبد أرتميس", "معبد زيوس"],
        correctAnswer: "معبد الكرنك",
      ),
      QuestionModel(
        question: "من هو فرعون مصر الذي ارتبط اسمه بالهرم الأكبر في الجيزة؟",
        options: ["رمسيس الثاني", "توت عنخ آمون", "خوفو", "أخناتون"],
        correctAnswer: "خوفو",
      ),
      QuestionModel(
        question: "ما هو اسم الملحمة الأدبية الشهيرة في حضارة بلاد الرافدين؟",
        options: ["الإلياذة", "الأوديسة", "ملحمة جلجامش", "كتاب الموتى"],
        correctAnswer: "ملحمة جلجامش",
      ),
      QuestionModel(
        question: "أي من الحضارات التالية اخترعت العجلة؟",
        options: ["الحضارة المصرية", "الحضارة السومرية", "الحضارة الهندية", "الحضارة الصينية"],
        correctAnswer: "الحضارة السومرية",
      ),
    ],
  ),

  // المستوى 4: الإمبراطوريات القديمة
  LevelModel(
    level: 4,
    title: "الإمبراطوريات القديمة",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFF0D47A1),
      Color(0xFF1565C0),
      Color(0xFF1976D2),
      Color(0xFF1E88E5),
    ],
    questions: [
      QuestionModel(
        question: "من هو مؤسس الإمبراطورية الرومانية؟",
        options: ["يوليوس قيصر", "أوكتافيوس (أغسطس)", "نيرون", "قسطنطين"],
        correctAnswer: "أوكتافيوس (أغسطس)",
      ),
      QuestionModel(
        question: "متى سقطت الإمبراطورية الرومانية الغربية؟",
        options: ["313م", "476م", "527م", "632م"],
        correctAnswer: "476م",
      ),
      QuestionModel(
        question: "ما اسم السلالة التي حكمت الصين لأطول فترة؟",
        options: ["سلالة هان", "سلالة مينغ", "سلالة تانغ", "سلالة تشينغ"],
        correctAnswer: "سلالة هان",
      ),
      QuestionModel(
        question: "من هو مؤسس الإمبراطورية الفارسية؟",
        options: ["قورش العظيم", "داريوس الأول", "أحشويروش", "كسرى أنوشروان"],
        correctAnswer: "قورش العظيم",
      ),
      QuestionModel(
        question: "من هو الإمبراطور البيزنطي الذي أصدر مدونة القوانين الشهيرة؟",
        options: ["قسطنطين", "ثيودوسيوس", "جستنيان", "هرقل"],
        correctAnswer: "جستنيان",
      ),
      QuestionModel(
        question: "ماذا كانت تسمى الإمبراطورية الفارسية في عهد كورش الكبير؟",
        options: ["الإمبراطورية الساسانية", "الإمبراطورية البارثية", "الإمبراطورية الأخمينية", "الإمبراطورية الميدية"],
        correctAnswer: "الإمبراطورية الأخمينية",
      ),
      QuestionModel(
        question: "ما اسم عاصمة الإمبراطورية البيزنطية؟",
        options: ["روما", "أثينا", "الإسكندرية", "القسطنطينية"],
        correctAnswer: "القسطنطينية",
      ),
      QuestionModel(
        question: "من هو القائد المقدوني الذي أسس إمبراطورية تمتد من اليونان إلى الهند؟",
        options: ["الإسكندر الأكبر", "فيليب المقدوني", "هانيبال", "سقراط"],
        correctAnswer: "الإسكندر الأكبر",
      ),
      QuestionModel(
        question: "أي إمبراطور روماني جعل المسيحية الدين الرسمي للإمبراطورية؟",
        options: ["قسطنطين", "ثيودوسيوس الأول", "نيرون", "ماركوس أوريليوس"],
        correctAnswer: "ثيودوسيوس الأول",
      ),
      QuestionModel(
        question: "ما هي الإمبراطورية التي تأسست على يد جنكيز خان؟",
        options: ["الإمبراطورية العثمانية", "الإمبراطورية المغولية", "الإمبراطورية الصينية", "الإمبراطورية الفارسية"],
        correctAnswer: "الإمبراطورية المغولية",
      ),
    ],
  ),

  // المستوى 5: العصور الوسطى
  LevelModel(
    level: 5,
    title: "العصور الوسطى",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFF004D40),
      Color(0xFF00695C),
      Color(0xFF00796B),
      Color(0xFF00897B),
    ],
    questions: [
      QuestionModel(
        question: "متى بدأت الحروب الصليبية؟",
        options: ["عام 1066م", "عام 1096م", "عام 1187م", "عام 1204م"],
        correctAnswer: "عام 1096م",
      ),
      QuestionModel(
        question: "من هو القائد المسلم الذي حرر بيت المقدس من الصليبيين؟",
        options: ["صلاح الدين الأيوبي", "نور الدين زنكي", "سيف الدين قطز", "الظاهر بيبرس"],
        correctAnswer: "صلاح الدين الأيوبي",
      ),
      QuestionModel(
        question: "متى بدأت الدولة الأموية؟",
        options: ["661م", "750م", "570م", "632م"],
        correctAnswer: "661م",
      ),
      QuestionModel(
        question: "ما هي عاصمة الدولة العباسية؟",
        options: ["دمشق", "القاهرة", "بغداد", "القيروان"],
        correctAnswer: "بغداد",
      ),
      QuestionModel(
        question: "ما اسم المجمع العلمي الذي أسسه المأمون لترجمة العلوم؟",
        options: ["المدرسة المستنصرية", "بيت الحكمة", "دار العلوم", "مكتبة الإسكندرية"],
        correctAnswer: "بيت الحكمة",
      ),
      QuestionModel(
        question: "من هو المؤرخ المسلم صاحب كتاب 'المقدمة' ومؤسس علم الاجتماع؟",
        options: ["ابن خلدون", "الطبري", "المقريزي", "المسعودي"],
        correctAnswer: "ابن خلدون",
      ),
      QuestionModel(
        question: "متى وقعت معركة عين جالوت التي توقف فيها زحف المغول نحو مصر والشام؟",
        options: ["1258م", "1260م", "1300م", "1220م"],
        correctAnswer: "1260م",
      ),
      QuestionModel(
        question: "ما اسم الوباء الذي ضرب أوروبا في القرن الرابع عشر وقضى على ثلث سكانها؟",
        options: ["الجدري", "الكوليرا", "الطاعون الأسود", "الحمى الصفراء"],
        correctAnswer: "الطاعون الأسود",
      ),
      QuestionModel(
        question: "ما هو النظام الاقتصادي والاجتماعي السائد في أوروبا خلال العصور الوسطى؟",
        options: ["النظام الإمبراطوري", "نظام الإقطاع", "النظام الرأسمالي", "النظام الديمقراطي"],
        correctAnswer: "نظام الإقطاع",
      ),
      QuestionModel(
        question: "من هو أشهر ملوك الفرنجة وأسس الإمبراطورية الكارولنجية؟",
        options: ["شارلمان", "لويس التقي", "شارل مارتل", "بيبين القصير"],
        correctAnswer: "شارلمان",
      ),
    ],
  ),

  // المستوى 6: الاكتشافات والنهضة
  LevelModel(
    level: 6,
    title: "الاكتشافات والنهضة",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF7B1FA2),
      Color(0xFF8E24AA),
    ],
    questions: [
      QuestionModel(
        question: "متى بدأ عصر النهضة في أوروبا؟",
        options: ["القرن الثالث عشر", "القرن الرابع عشر", "القرن السادس عشر", "القرن الثامن عشر"],
        correctAnswer: "القرن الرابع عشر",
      ),
      QuestionModel(
        question: "من هو الفنان الإيطالي الذي رسم لوحة الموناليزا؟",
        options: ["رافائيل", "ليوناردو دا فينشي", "مايكل أنجلو", "تيتيان"],
        correctAnswer: "ليوناردو دا فينشي",
      ),
      QuestionModel(
        question: "من هو الرحالة الذي اكتشف أمريكا عام 1492م؟",
        options: ["فرديناند ماجلان", "كريستوفر كولومبوس", "أميريغو فسبوتشي", "فاسكو دا جاما"],
        correctAnswer: "كريستوفر كولومبوس",
      ),
      QuestionModel(
        question: "من هو أول من دار حول العالم في رحلة بحرية؟",
        options: ["فرديناند ماجلان وطاقمه", "كريستوفر كولومبوس", "فاسكو دا جاما", "جيمس كوك"],
        correctAnswer: "فرديناند ماجلان وطاقمه",
      ),
      QuestionModel(
        question: "أين بدأت حركة النهضة الأوروبية؟",
        options: ["فرنسا", "إنجلترا", "إيطاليا", "ألمانيا"],
        correctAnswer: "إيطاليا",
      ),
      QuestionModel(
        question: "ما هي الحركة الفكرية التي ميزت عصر النهضة؟",
        options: ["الرومانسية", "الإنسانية", "الوجودية", "التجريبية"],
        correctAnswer: "الإنسانية",
      ),
      QuestionModel(
        question: "من هو مؤلف كتاب 'الأمير'؟",
        options: ["نيكولو مكيافيللي", "توماس مور", "إيرازموس", "جيوفاني بوكاتشيو"],
        correctAnswer: "نيكولو مكيافيللي",
      ),
      QuestionModel(
        question: "من هو عالم الفلك البولندي الذي اقترح أن الأرض تدور حول الشمس؟",
        options: ["جاليليو جاليلي", "يوهانس كبلر", "نيكولاس كوبرنيكوس", "إسحاق نيوتن"],
        correctAnswer: "نيكولاس كوبرنيكوس",
      ),
      QuestionModel(
        question: "ما هو اسم الطريق البحري الذي اكتشفه فاسكو دا جاما إلى الهند؟",
        options: ["طريق رأس الرجاء الصالح", "طريق الحرير", "مضيق ماجلان", "طريق الهند الشرقية"],
        correctAnswer: "طريق رأس الرجاء الصالح",
      ),
      QuestionModel(
        question: "ما هو الاختراع الذي ساعد في نشر المعرفة خلال عصر النهضة؟",
        options: ["البوصلة", "البارود", "المطبعة", "التلسكوب"],
        correctAnswer: "المطبعة",
      ),
    ],
  ),

  // المستوى 7: الثورات والإمبراطوريات الحديثة
  LevelModel(
    level: 7,
    title: "الثورات والإمبراطوريات الحديثة",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFFB71C1C),
      Color(0xFFC62828),
      Color(0xFFD32F2F),
      Color(0xFFE53935),
    ],
    questions: [
      QuestionModel(
        question: "متى بدأت الثورة الفرنسية؟",
        options: ["1776م", "1789م", "1804م", "1812م"],
        correctAnswer: "1789م",
      ),
      QuestionModel(
        question: "من هي آخر سلالة إمبراطورية حكمت الصين؟",
        options: ["سلالة مينغ", "سلالة تانغ", "سلالة تشينغ", "سلالة يوان"],
        correctAnswer: "سلالة تشينغ",
      ),
      QuestionModel(
        question: "من هو القائد العسكري والسياسي الذي أصبح إمبراطوراً لفرنسا بعد الثورة الفرنسية؟",
        options: ["لويس السادس عشر", "نابليون بونابرت", "ماكسيميليان روبسبير", "شارل ديغول"],
        correctAnswer: "نابليون بونابرت",
      ),
      QuestionModel(
        question: "ما الحدث الذي يعتبر بداية الثورة الفرنسية؟",
        options: ["اقتحام سجن الباستيل", "محاكمة لويس السادس عشر", "إعلان الجمهورية", "مسيرة النساء إلى فرساي"],
        correctAnswer: "اقتحام سجن الباستيل",
      ),
      QuestionModel(
        question: "من قاد الثورة البلشفية في روسيا عام 1917؟",
        options: ["جوزيف ستالين", "ليون تروتسكي", "فلاديمير لينين", "القيصر نيكولاس"],
        correctAnswer: "فلاديمير لينين",
      ),
      QuestionModel(
        question: "ما هي الدولة التي حكمت الهند قبل استقلالها عام 1947؟",
        options: ["فرنسا", "بريطانيا", "البرتغال", "هولندا"],
        correctAnswer: "بريطانيا",
      ),
      QuestionModel(
        question: "أي من الشخصيات التالية قاد كفاح الاستقلال في الهند ضد الاستعمار البريطاني؟",
        options: ["نهرو", "جناح", "غاندي", "أمبيدكار"],
        correctAnswer: "غاندي",
      ),
      QuestionModel(
        question: "ما اسم الثورة التي أطاحت بالقيصر في روسيا عام 1917؟",
        options: ["الثورة البلشفية", "الثورة البيضاء", "ثورة فبراير", "ثورة أكتوبر"],
        correctAnswer: "ثورة فبراير",
      ),
      QuestionModel(
        question: "متى أصبحت الولايات المتحدة الأمريكية دولة مستقلة؟",
        options: ["1776م", "1783م", "1789م", "1800م"],
        correctAnswer: "1776م",
      ),
      QuestionModel(
        question: "ما هي الحركة التي قادها المهاتما غاندي في الهند؟",
        options: ["الثورة المسلحة", "حركة عدم الانحياز", "حركة المقاومة المدنية", "الحركة الشيوعية"],
        correctAnswer: "حركة المقاومة المدنية",
      ),
    ],
  ),

  // المستوى 8: الحروب العالمية
  LevelModel(
    level: 8,
    title: "الحروب العالمية",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFF1A237E),
      Color(0xFF283593),
      Color(0xFF303F9F),
      Color(0xFF3949AB),
    ],
    questions: [
      QuestionModel(
        question: "متى بدأت الحرب العالمية الأولى؟",
        options: ["عام 1910", "عام 1914", "عام 1918", "عام 1920"],
        correctAnswer: "عام 1914",
      ),
      QuestionModel(
        question: "ما هو الحدث الذي أشعل فتيل الحرب العالمية الأولى؟",
        options: ["غزو ألمانيا لبولندا", "اغتيال ولي عهد النمسا", "إعلان روسيا الحرب على اليابان", "سقوط الإمبراطورية العثمانية"],
        correctAnswer: "اغتيال ولي عهد النمسا",
      ),
      QuestionModel(
        question: "متى بدأت الحرب العالمية الثانية في أوروبا؟",
        options: ["عام 1937", "عام 1939", "عام 1941", "عام 1945"],
        correctAnswer: "عام 1939",
      ),
      QuestionModel(
        question: "أي معركة تعتبر نقطة تحول في الحرب العالمية الثانية على الجبهة الشرقية؟",
        options: ["معركة العلمين", "معركة ستالينجراد", "معركة نورماندي", "معركة ميدواي"],
        correctAnswer: "معركة ستالينجراد",
      ),
      QuestionModel(
        question: "ما اسم المشروع الأمريكي السري لتطوير القنبلة الذرية؟",
        options: ["مشروع أبولو", "مشروع مانهاتن", "مشروع إنيجما", "مشروع هيدرا"],
        correctAnswer: "مشروع مانهاتن",
      ),
      QuestionModel(
        question: "على أي مدينتين يابانيتين ألقيت القنابل الذرية في الحرب العالمية الثانية؟",
        options: ["هيروشيما وناجازاكي", "طوكيو وكيوتو", "أوساكا وناجويا", "ناغانو وساپورو"],
        correctAnswer: "هيروشيما وناجازاكي",
      ),
      QuestionModel(
        question: "من كان زعيم ألمانيا النازية خلال الحرب العالمية الثانية؟",
        options: ["أدولف هتلر", "هاينريش هيملر", "هيرمان غورينغ", "يوزف غوبلز"],
        correctAnswer: "أدولف هتلر",
      ),
      QuestionModel(
        question: "ما هي معاهدة السلام التي فُرضت على ألمانيا بعد الحرب العالمية الأولى؟",
        options: ["معاهدة فرساي", "معاهدة بريست-ليتوفسك", "معاهدة سيفر", "معاهدة لوزان"],
        correctAnswer: "معاهدة فرساي",
      ),
      QuestionModel(
        question: "ما هي المعركة البحرية التي غيرت مجرى الحرب في المحيط الهادئ؟",
        options: ["معركة ميدواي", "معركة المرجان", "معركة ليتي", "معركة أوكيناوا"],
        correctAnswer: "معركة ميدواي",
      ),
      QuestionModel(
        question: "ما اسم عملية إنزال قوات الحلفاء في نورماندي بفرنسا عام 1944؟",
        options: ["عملية باربروسا", "عملية أوفرلورد", "عملية نيبتون", "عملية ماركت جاردن"],
        correctAnswer: "عملية أوفرلورد",
      ),
    ],
  ),

  // المستوى 9: المعاصر والفترة ما بعد الحرب العالمية الثانية
  LevelModel(
    level: 9,
    title: "التاريخ المعاصر",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFF33691E),
      Color(0xFF558B2F),
      Color(0xFF689F38),
      Color(0xFF7CB342),
    ],
    questions: [
      QuestionModel(
        question: "متى تأسست منظمة الأمم المتحدة؟",
        options: ["1945م", "1948م", "1950م", "1955م"],
        correctAnswer: "1945م",
      ),
      QuestionModel(
        question: "ما اسم الصراع الذي نشأ بين الكتلتين الشرقية والغربية بعد الحرب العالمية الثانية؟",
        options: ["الحرب العالمية الثالثة", "حرب المائة عام", "الحرب الباردة", "حرب النجوم"],
        correctAnswer: "الحرب الباردة",
      ),
      QuestionModel(
        question: "متى انهار الاتحاد السوفييتي؟",
        options: ["1985م", "1989م", "1991م", "1993م"],
        correctAnswer: "1991م",
      ),
      QuestionModel(
        question: "متى سقط جدار برلين؟",
        options: ["1985م", "1987م", "1989م", "1991م"],
        correctAnswer: "1989م",
      ),
      QuestionModel(
        question: "من هو أول رائد فضاء يدور حول الأرض؟",
        options: ["نيل أرمسترونج", "جون جلين", "يوري جاجارين", "باز ألدرين"],
        correctAnswer: "يوري جاجارين",
      ),
      QuestionModel(
        question: "ما اسم أزمة الصواريخ التي كادت تشعل حرباً نووية عام 1962م؟",
        options: ["أزمة الصواريخ الكورية", "أزمة الصواريخ الكوبية", "أزمة برلين", "أزمة فيتنام"],
        correctAnswer: "أزمة الصواريخ الكوبية",
      ),
      QuestionModel(
        question: "من هو أول من تم انتخابه رئيساً في جنوب أفريقيا بعد نظام الفصل العنصري؟",
        options: ["ثابو مبيكي", "نيلسون مانديلا", "ديزموند توتو", "فريدريك دي كلارك"],
        correctAnswer: "نيلسون مانديلا",
      ),
      QuestionModel(
        question: "ما هي الثورة التي أطاحت بشاه إيران عام 1979؟",
        options: ["الثورة البيضاء", "الثورة الخضراء", "الثورة الإسلامية", "ثورة المشروطة"],
        correctAnswer: "الثورة الإسلامية",
      ),
      QuestionModel(
        question: "أي حدث شهير وقع في 11 سبتمبر 2001؟",
        options: ["حرب الخليج الأولى", "هجمات الحادي عشر من سبتمبر", "غزو العراق", "الربيع العربي"],
        correctAnswer: "هجمات الحادي عشر من سبتمبر",
      ),
      QuestionModel(
        question: "متى تم تأسيس الاتحاد الأوروبي بشكله الحالي؟",
        options: ["1957م", "1973م", "1992م", "2002م"],
        correctAnswer: "1992م",
      ),
    ],
  ),

  // المستوى 10: تاريخ العلوم والاكتشافات
  LevelModel(
    level: 10,
    title: "تاريخ العلوم والاكتشافات",
    category: QuizCategory.historical,
    backgroundGradient: const [
      Color(0xFF880E4F),
      Color(0xFFA01555),
      Color(0xFFAD1457),
      Color(0xFFC2185B),
    ],
    questions: [
      QuestionModel(
        question: "من هو العالم الذي صاغ نظرية النسبية؟",
        options: ["نيوتن", "آينشتاين", "فاراداي", "ماكسويل"],
        correctAnswer: "آينشتاين",
      ),
      QuestionModel(
        question: "من هو مخترع المصباح الكهربائي العملي؟",
        options: ["نيكولا تسلا", "توماس إديسون", "ألكسندر جراهام بيل", "جيمس واط"],
        correctAnswer: "توماس إديسون",
      ),
      QuestionModel(
        question: "متى هبط الإنسان على سطح القمر لأول مرة؟",
        options: ["1957م", "1961م", "1969م", "1975م"],
        correctAnswer: "1969م",
      ),
      QuestionModel(
        question: "من هو العالم الذي اكتشف البنسلين؟",
        options: ["ألكسندر فلمنج", "لويس باستير", "روبرت كوخ", "جوناس سولك"],
        correctAnswer: "ألكسندر فلمنج",
      ),
      QuestionModel(
        question: "من هي أول امرأة حصلت على جائزة نوبل؟",
        options: ["روزاليند فرانكلين", "ماري كوري", "ليز مايتنر", "إيرين جوليو-كوري"],
        correctAnswer: "ماري كوري",
      ),
      QuestionModel(
        question: "من هو العالم المسلم الذي اخترع الجبر؟",
        options: ["الخوارزمي", "ابن الهيثم", "ابن سينا", "البيروني"],
        correctAnswer: "الخوارزمي",
      ),
      QuestionModel(
        question: "أي اختراع يُنسب إلى جوتنبرغ في القرن الخامس عشر؟",
        options: ["البوصلة", "المطبعة ذات الأحرف المتحركة", "التلسكوب", "الساعة الميكانيكية"],
        correctAnswer: "المطبعة ذات الأحرف المتحركة",
      ),
      QuestionModel(
        question: "من هو العالم الذي وضع قوانين الحركة والجاذبية؟",
        options: ["غاليليو", "نيوتن", "كبلر", "أينشتاين"],
        correctAnswer: "نيوتن",
      ),
      QuestionModel(
        question: "متى تم اكتشاف الحمض النووي DNA؟",
        options: ["1869م", "1953م", "1975م", "1995م"],
        correctAnswer: "1953م",
      ),
      QuestionModel(
        question: "من هو العالم الذي طور نظرية التطور عن طريق الانتقاء الطبيعي؟",
        options: ["تشارلز داروين", "جريجور مندل", "لويس باستير", "ألفريد راسل والاس"],
        correctAnswer: "تشارلز داروين",
      ),
    ],
  ),
];

/// --- 4. قائمة مستويات ألعاب الفيديو ---
final List<LevelModel> videoGamesLevels = [
  // المستوى 1: أساسيات ألعاب الفيديو
  LevelModel(
    level: 1,
    title: "أساسيات ألعاب الفيديو",
    category: QuizCategory.videoGames,
    isUnlocked: true,
    backgroundGradient: const [
      Color(0xFF6A0DAD),
      Color(0xFF9370DB),
      Color(0xFFB19CD9),
      Color(0xFFD8BFD8),
    ],
    questions: [
      QuestionModel(
        question: "ما هي أكثر منصة ألعاب مبيعًا على مر التاريخ؟",
        options: ["PlayStation 2", "Nintendo DS", "Xbox 360", "Nintendo Switch"],
        correctAnswer: "PlayStation 2",
      ),
      QuestionModel(
        question: "من هي الشركة المصنعة للعبة Fortnite؟",
        options: ["Activision", "Epic Games", "EA Sports", "Ubisoft"],
        correctAnswer: "Epic Games",
      ),
      QuestionModel(
        question: "أي من الشخصيات التالية هي رمز شركة Nintendo؟",
        options: ["Sonic", "Mario", "Crash Bandicoot", "Master Chief"],
        correctAnswer: "Mario",
      ),
      QuestionModel(
        question: "ما هي اللعبة الأكثر مبيعًا في تاريخ ألعاب الفيديو؟",
        options: ["Grand Theft Auto V", "Minecraft", "Tetris", "Wii Sports"],
        correctAnswer: "Minecraft",
      ),
      QuestionModel(
        question: "في أي عام تم إصدار أول جهاز PlayStation؟",
        options: ["1990", "1994", "1998", "2000"],
        correctAnswer: "1994",
      ),
      QuestionModel(
        question: "من هو بطل سلسلة ألعاب The Legend of Zelda؟",
        options: ["Zelda", "Ganondorf", "Link", "Epona"],
        correctAnswer: "Link",
      ),
      QuestionModel(
        question: "ما هي لعبة Battle Royale الشهيرة التي تم إصدارها بواسطة شركة Bluehole؟",
        options: ["Fortnite", "Apex Legends", "PUBG", "Call of Duty: Warzone"],
        correctAnswer: "PUBG",
      ),
      QuestionModel(
        question: "ما اسم الشخصية الرئيسية في لعبة God of War؟",
        options: ["Zeus", "Kratos", "Ares", "Thor"],
        correctAnswer: "Kratos",
      ),
      QuestionModel(
        question: "في أي عام تم إصدار أول جهاز Xbox؟",
        options: ["1998", "2001", "2005", "2010"],
        correctAnswer: "2001",
      ),
      QuestionModel(
        question: "ما هي اللعبة التي اشتهرت بشخصية Agent 47؟",
        options: ["Metal Gear Solid", "Hitman", "Splinter Cell", "Assassin's Creed"],
        correctAnswer: "Hitman",
      ),
    ],
  ),
  
  // المستوى 2: ألعاب كلاسيكية
  LevelModel(
    level: 2,
    title: "ألعاب كلاسيكية",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFF6A0DAD),
      Color(0xFF9370DB),
      Color(0xFFB19CD9),
      Color(0xFFD8BFD8),
    ],
    questions: [
      QuestionModel(
        question: "في أي عام تم إصدار لعبة Pac-Man؟",
        options: ["1975", "1980", "1985", "1990"],
        correctAnswer: "1980",
      ),
      QuestionModel(
        question: "ما هو اسم شخصية الثعبان الأيقوني في سلسلة ألعاب Metal Gear Solid؟",
        options: ["Venom Snake", "Liquid Snake", "Solid Snake", "Snake Eyes"],
        correctAnswer: "Solid Snake",
      ),
      QuestionModel(
        question: "ما هي اللعبة الأولى التي ظهر فيها السباك الشهير ماريو؟",
        options: ["Super Mario Bros", "Donkey Kong", "Mario Bros", "Mario Kart"],
        correctAnswer: "Donkey Kong",
      ),
      QuestionModel(
        question: "ما هو اسم المدينة الخيالية في لعبة Grand Theft Auto: San Andreas؟",
        options: ["Los Santos", "Vice City", "Liberty City", "Bullworth"],
        correctAnswer: "Los Santos",
      ),
      QuestionModel(
        question: "ما هي اللعبة التي اشتهرت بمقولة 'Finish Him!'؟",
        options: ["Street Fighter", "Tekken", "Mortal Kombat", "Soul Calibur"],
        correctAnswer: "Mortal Kombat",
      ),
      QuestionModel(
        question: "ما هو اسم الشركة المطورة للعبة The Elder Scrolls V: Skyrim؟",
        options: ["Blizzard", "Bethesda", "BioWare", "Bungie"],
        correctAnswer: "Bethesda",
      ),
      QuestionModel(
        question: "أي جزء من سلسلة Final Fantasy قدم نظام المعارك القائم على الزمن (ATB)؟",
        options: ["Final Fantasy III", "Final Fantasy IV", "Final Fantasy VI", "Final Fantasy VII"],
        correctAnswer: "Final Fantasy IV",
      ),
      QuestionModel(
        question: "ما هي اللعبة الكلاسيكية التي تتضمن شخصية Sonic the Hedgehog؟",
        options: ["Crash Bandicoot", "Pac-Man", "Sonic the Hedgehog", "Mega Man"],
        correctAnswer: "Sonic the Hedgehog",
      ),
      QuestionModel(
        question: "ما اسم اللعبة التي تتضمن شخصيات تقاتل في ميدان معركة وتعتبر من أشهر ألعاب Nintendo؟",
        options: ["Street Fighter", "Tekken", "Super Smash Bros", "Mortal Kombat"],
        correctAnswer: "Super Smash Bros",
      ),
      QuestionModel(
        question: "ما هي اللعبة التي اشتهرت بمكعباتها ونظام البناء الحر؟",
        options: ["Terraria", "Roblox", "Minecraft", "Fortnite"],
        correctAnswer: "Minecraft",
      ),
    ],
  ),
  
  // المستوى 3: ألعاب المغامرات
  LevelModel(
    level: 3,
    title: "ألعاب المغامرات",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFF6A0DAD),
      Color(0xFF9370DB),
      Color(0xFFB19CD9),
      Color(0xFFD8BFD8),
    ],
    questions: [
      QuestionModel(
        question: "ما اسم بطل سلسلة ألعاب Uncharted؟",
        options: ["جويل", "كراتوس", "ناثان دريك", "ماركوس فينيكس"],
        correctAnswer: "ناثان دريك",
      ),
      QuestionModel(
        question: "ما اسم البطلة الرئيسية في سلسلة ألعاب Tomb Raider؟",
        options: ["أليكسا", "لارا كروفت", "جيل فالنتاين", "فيث كونورز"],
        correctAnswer: "لارا كروفت",
      ),
      QuestionModel(
        question: "ما هي اللعبة التي تدور أحداثها في مدينة رابتشر تحت الماء؟",
        options: ["Half-Life", "BioShock", "Doom", "Prey"],
        correctAnswer: "BioShock",
      ),
      QuestionModel(
        question: "أي من هذه الألعاب تعتمد على استكشاف عالم ما بعد نهاية العالم؟",
        options: ["The Last of Us", "FIFA", "Gran Turismo", "Just Dance"],
        correctAnswer: "The Last of Us",
      ),
      QuestionModel(
        question: "ما هي لعبة المغامرات التي يتحكم فيها اللاعب بصبي يدعى 'بوي' ويرافقه والده 'كراتوس'؟",
        options: ["Uncharted 4", "God of War (2018)", "The Last Guardian", "Hellblade: Senua's Sacrifice"],
        correctAnswer: "God of War (2018)",
      ),
      QuestionModel(
        question: "ما هي اللعبة التي يتحكم فيها اللاعب بشخصية تدعى 'ألوي' وتحارب روبوتات على شكل حيوانات؟",
        options: ["Control", "Horizon Zero Dawn", "Detroit: Become Human", "Death Stranding"],
        correctAnswer: "Horizon Zero Dawn",
      ),
      QuestionModel(
        question: "ما هي لعبة المغامرات التي تدور في جزيرة مملوءة بالديناصورات؟",
        options: ["Far Cry Primal", "Monster Hunter World", "Jurassic World Evolution", "ARK: Survival Evolved"],
        correctAnswer: "ARK: Survival Evolved",
      ),
      QuestionModel(
        question: "ما اسم العالم المفتوح في لعبة The Legend of Zelda: Breath of the Wild؟",
        options: ["هيرول", "هايرول", "تاميريل", "لوردران"],
        correctAnswer: "هايرول",
      ),
      QuestionModel(
        question: "ما هي لعبة المغامرات التي تتضمن استكشاف كواكب مختلفة مع روبوت صغير يدعى BD-1؟",
        options: ["Star Wars Jedi: Fallen Order", "Mass Effect", "No Man's Sky", "The Outer Worlds"],
        correctAnswer: "Star Wars Jedi: Fallen Order",
      ),
      QuestionModel(
        question: "ما هي لعبة المغامرات التي يلعب فيها اللاعب دور شخص يدعى 'آرثر مورغان'؟",
        options: ["Grand Theft Auto V", "Red Dead Redemption 2", "Assassin's Creed Valhalla", "Far Cry 5"],
        correctAnswer: "Red Dead Redemption 2",
      ),
    ],
  ),

  // المستوى 4: ألعاب الرياضة
  LevelModel(
    level: 4,
    title: "ألعاب الرياضة",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFF006064),
      Color(0xFF00838F),
      Color(0xFF0097A7),
      Color(0xFF00ACC1),
    ],
    questions: [
      QuestionModel(
        question: "ما هي الشركة المطورة لسلسلة ألعاب FIFA؟",
        options: ["Konami", "2K Sports", "EA Sports", "Ubisoft"],
        correctAnswer: "EA Sports",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تركز على رياضة كرة السلة؟",
        options: ["FIFA", "NHL", "WWE 2K", "NBA 2K"],
        correctAnswer: "NBA 2K",
      ),
      QuestionModel(
        question: "ما اسم لعبة سباقات السيارات الشهيرة التي تنتجها Sony؟",
        options: ["Forza Motorsport", "Need for Speed", "Gran Turismo", "Project CARS"],
        correctAnswer: "Gran Turismo",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تركز على المصارعة الحرة؟",
        options: ["UFC", "WWE 2K", "Fight Night", "Tekken"],
        correctAnswer: "WWE 2K",
      ),
      QuestionModel(
        question: "ما هي لعبة الرياضات الإلكترونية التي تتضمن سيارات تلعب كرة القدم؟",
        options: ["Forza Horizon", "TrackMania", "Rocket League", "Gran Turismo Sport"],
        correctAnswer: "Rocket League",
      ),
      QuestionModel(
        question: "ما اسم لعبة الجولف الأكثر شعبية على مر السنين؟",
        options: ["EA Sports PGA Tour", "Mario Golf", "Tiger Woods PGA Tour", "The Golf Club"],
        correctAnswer: "Tiger Woods PGA Tour",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تركز على رياضة التنس؟",
        options: ["Top Spin", "SSX", "FIFA Street", "Tony Hawk's Pro Skater"],
        correctAnswer: "Top Spin",
      ),
      QuestionModel(
        question: "ما هي لعبة سباق السيارات التي تتميز بأسلوب القيادة غير الواقعي والمثير؟",
        options: ["Forza Motorsport", "Gran Turismo", "Project CARS", "Need for Speed"],
        correctAnswer: "Need for Speed",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تركز على رياضة كرة القدم الأمريكية؟",
        options: ["MLB The Show", "FIFA", "Madden NFL", "NBA 2K"],
        correctAnswer: "Madden NFL",
      ),
      QuestionModel(
        question: "ما هي لعبة سباقات الدراجات النارية الأكثر شهرة؟",
        options: ["MotoGP", "F1", "DiRT Rally", "WRC"],
        correctAnswer: "MotoGP",
      ),
    ],
  ),

  // المستوى 5: ألعاب الأكشن
  LevelModel(
    level: 5,
    title: "ألعاب الأكشن",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF388E3C),
      Color(0xFF43A047),
    ],
    questions: [
      QuestionModel(
        question: "ما اسم بطل سلسلة ألعاب Devil May Cry؟",
        options: ["نيرو", "دانتي", "فيرجيل", "سبارد"],
        correctAnswer: "دانتي",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية طورتها شركة FromSoftware وتشتهر بصعوبتها العالية؟",
        options: ["Dark Souls", "The Witcher", "The Elder Scrolls", "Fallout"],
        correctAnswer: "Dark Souls",
      ),
      QuestionModel(
        question: "ما اسم اللعبة التي يلعب فيها اللاعب دور عميل خاص يدعى 'سام فيشر'؟",
        options: ["Metal Gear Solid", "Hitman", "Splinter Cell", "Max Payne"],
        correctAnswer: "Splinter Cell",
      ),
      QuestionModel(
        question: "ما اسم سلسلة ألعاب الأكشن التي تتميز بقدرة اللاعب على إبطاء الزمن (Bullet Time)؟",
        options: ["Max Payne", "Call of Duty", "Battlefield", "Medal of Honor"],
        correctAnswer: "Max Payne",
      ),
      QuestionModel(
        question: "ما هي اللعبة التي يلعب فيها اللاعب دور قاتل مأجور يُعرف باسم 'Agent 47'؟",
        options: ["Splinter Cell", "Metal Gear Solid", "Hitman", "Dishonored"],
        correctAnswer: "Hitman",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تركز على مكافحة الزومبي في مدينة راكون سيتي؟",
        options: ["Left 4 Dead", "Dead Rising", "The Last of Us", "Resident Evil"],
        correctAnswer: "Resident Evil",
      ),
      QuestionModel(
        question: "ما اسم اللعبة التي تدور أحداثها في مدينة خيالية تدعى 'كولومبيا' تطفو في السماء؟",
        options: ["BioShock", "BioShock 2", "BioShock Infinite", "System Shock"],
        correctAnswer: "BioShock Infinite",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تتميز بمكانيكية 'تمزيق وتقطيع' الأعداء؟",
        options: ["Devil May Cry", "Metal Gear Rising: Revengeance", "Bayonetta", "God of War"],
        correctAnswer: "Metal Gear Rising: Revengeance",
      ),
      QuestionModel(
        question: "ما اسم بطل سلسلة ألعاب Gears of War؟",
        options: ["ماركوس فينيكس", "ناثان دريك", "ماستر تشيف", "جويل ميلر"],
        correctAnswer: "ماركوس فينيكس",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تمزج بين عناصر التسلل والأكشن في بيئة خيالية؟",
        options: ["Thief", "Dishonored", "Hitman", "Splinter Cell"],
        correctAnswer: "Dishonored",
      ),
    ],
  ),

  // المستوى 6: ألعاب تقمص الأدوار (RPG)
  LevelModel(
    level: 6,
    title: "ألعاب تقمص الأدوار",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF7B1FA2),
      Color(0xFF8E24AA),
    ],
    questions: [
      QuestionModel(
        question: "ما اسم عالم لعبة The Elder Scrolls V: Skyrim؟",
        options: ["فيرلدن", "تاميريل", "لوردران", "أيبان"],
        correctAnswer: "تاميريل",
      ),
      QuestionModel(
        question: "أي سلسلة ألعاب RPG يابانية تشتهر بوجود مخلوقات تدعى 'تشوكوبو' و'موغلي'؟",
        options: ["Dragon Quest", "Persona", "Kingdom Hearts", "Final Fantasy"],
        correctAnswer: "Final Fantasy",
      ),
      QuestionModel(
        question: "من هو بطل سلسلة ألعاب The Witcher؟",
        options: ["إزيك", "غيرالت", "دانديليون", "فيسيمير"],
        correctAnswer: "غيرالت",
      ),
      QuestionModel(
        question: "ما هي لعبة RPG التي تتضمن استكشاف عالم ما بعد نهاية العالم في واشنطن العاصمة؟",
        options: ["Metro Exodus", "Fallout 3", "S.T.A.L.K.E.R.", "The Division"],
        correctAnswer: "Fallout 3",
      ),
      QuestionModel(
        question: "ما اسم مطوّر سلسلة ألعاب Dark Souls؟",
        options: ["FromSoftware", "Bethesda", "BioWare", "Square Enix"],
        correctAnswer: "FromSoftware",
      ),
      QuestionModel(
        question: "ما هي لعبة RPG التي تتميز بنظام 'الحوار األخلاقي' ذي الألوان المميزة؟",
        options: ["The Elder Scrolls", "Dragon Age", "Mass Effect", "Fable"],
        correctAnswer: "Mass Effect",
      ),
      QuestionModel(
        question: "ما اسم اللعبة التي تتضمن عالماً مفتوحاً يدعى 'سكايريم'؟",
        options: ["The Elder Scrolls IV: Oblivion", "The Elder Scrolls V: Skyrim", "The Elder Scrolls Online", "Fallout 4"],
        correctAnswer: "The Elder Scrolls V: Skyrim",
      ),
      QuestionModel(
        question: "أي من ألعاب RPG التالية تتميز بوجود 'حوارات' تؤثر بشكل كبير على مجريات القصة؟",
        options: ["Dark Souls", "Monster Hunter", "Dragon Age", "Diablo"],
        correctAnswer: "Dragon Age",
      ),
      QuestionModel(
        question: "ما هي لعبة تقمص الأدوار التي تتضمن شخصية 'سيفيروث' كشرير رئيسي؟",
        options: ["Final Fantasy VI", "Final Fantasy VII", "Final Fantasy VIII", "Final Fantasy X"],
        correctAnswer: "Final Fantasy VII",
      ),
      QuestionModel(
        question: "أي من ألعاب RPG التالية تتضمن عناصر من الميثولوجيا اليونانية والرومانية؟",
        options: ["God of War", "Assassin's Creed Odyssey", "Ryse: Son of Rome", "Immortals Fenyx Rising"],
        correctAnswer: "Assassin's Creed Odyssey",
      ),
    ],
  ),

  // المستوى 7: ألعاب الاستراتيجية
  LevelModel(
    level: 7,
    title: "ألعاب الاستراتيجية",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFF0D47A1),
      Color(0xFF1565C0),
      Color(0xFF1976D2),
      Color(0xFF1E88E5),
    ],
    questions: [
      QuestionModel(
        question: "أي من الألعاب التالية تعتبر من ألعاب استراتيجية الوقت الحقيقي (RTS)؟",
        options: ["XCOM", "Civilization", "StarCraft", "Into the Breach"],
        correctAnswer: "StarCraft",
      ),
      QuestionModel(
        question: "ما هي لعبة الاستراتيجية التي تتيح للاعبين بناء حضارة من العصر الحجري وحتى المستقبل؟",
        options: ["Age of Empires", "Total War", "Civilization", "Europa Universalis"],
        correctAnswer: "Civilization",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تعتبر من ألعاب MOBA؟",
        options: ["Overwatch", "Counter-Strike", "StarCraft II", "League of Legends"],
        correctAnswer: "League of Legends",
      ),
      QuestionModel(
        question: "ما هو مطوّر لعبة Dota 2؟",
        options: ["Blizzard", "Valve", "Riot Games", "Epic Games"],
        correctAnswer: "Valve",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تعتبر من ألعاب الاستراتيجية القائمة على الأدوار (Turn-Based Strategy)؟",
        options: ["StarCraft", "Company of Heroes", "Command & Conquer", "XCOM"],
        correctAnswer: "XCOM",
      ),
      QuestionModel(
        question: "ما هي اللعبة الاستراتيجية التي تتضمن مجموعات من الوحدات تُعرف باسم 'الأبطال' (Heroes)؟",
        options: ["Warcraft III", "Age of Empires II", "Command & Conquer", "Rise of Nations"],
        correctAnswer: "Warcraft III",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تُعتبر من ألعاب استراتيجية 4X؟",
        options: ["Total War: Warhammer", "Stellaris", "Command & Conquer", "Company of Heroes"],
        correctAnswer: "Stellaris",
      ),
      QuestionModel(
        question: "ما هي لعبة الاستراتيجية التاريخية التي تُركز على الحروب الكبرى في التاريخ؟",
        options: ["Age of Empires", "Total War", "Europa Universalis", "Hearts of Iron"],
        correctAnswer: "Total War",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية هي لعبة استراتيجية تُركز على الحرب العالمية الثانية؟",
        options: ["Europa Universalis IV", "Crusader Kings III", "Victoria II", "Hearts of Iron IV"],
        correctAnswer: "Hearts of Iron IV",
      ),
      QuestionModel(
        question: "ما هي لعبة استراتيجية الوقت الحقيقي التي تدور أحداثها في عالم Warhammer 40,000؟",
        options: ["Dawn of War", "Starcraft", "Command & Conquer", "Company of Heroes"],
        correctAnswer: "Dawn of War",
      ),
    ],
  ),

  // المستوى 8: ألعاب التصويب من منظور الشخص الأول (FPS)
  LevelModel(
    level: 8,
    title: "ألعاب التصويب",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFFB71C1C),
      Color(0xFFC62828),
      Color(0xFFD32F2F),
      Color(0xFFE53935),
    ],
    questions: [
      QuestionModel(
        question: "ما هو اسم البطل الرئيسي في سلسلة ألعاب Halo؟",
        options: ["ماركوس فينيكس", "ماستر تشيف", "الكابتن برايس", "سبارتان-117"],
        correctAnswer: "ماستر تشيف",
      ),
      QuestionModel(
        question: "ما هي لعبة التصويب التي تتميز بنمط اللعب Battle Royale واسمها 'ساحة معارك اللاعبين المجهولين'؟",
        options: ["Apex Legends", "Fortnite", "PUBG", "Call of Duty: Warzone"],
        correctAnswer: "PUBG",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية شهدت أول ظهور لشخصية 'جوردون فريمان'؟",
        options: ["Half-Life", "Doom", "Quake", "Unreal"],
        correctAnswer: "Half-Life",
      ),
      QuestionModel(
        question: "ما هي لعبة التصويب من منظور الشخص الأول التي تُعتبر من أقدم الألعاب في هذا النوع؟",
        options: ["Wolfenstein 3D", "Doom", "Quake", "Duke Nukem 3D"],
        correctAnswer: "Wolfenstein 3D",
      ),
      QuestionModel(
        question: "ما هي سلسلة ألعاب التصويب الشهيرة التي طورتها شركة Infinity Ward؟",
        options: ["Battlefield", "Medal of Honor", "Call of Duty", "Halo"],
        correctAnswer: "Call of Duty",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تُعرف بأسلوب لعب 'حركة سريعة' وأعداء مخيفين؟",
        options: ["Doom", "Call of Duty", "Counter-Strike", "Rainbow Six Siege"],
        correctAnswer: "Doom",
      ),
      QuestionModel(
        question: "ما هي لعبة التصويب التي تتميز بفرق من 'عملاء' ومهاجمين يتنافسون في جولات قصيرة؟",
        options: ["Call of Duty", "Valorant", "Overwatch", "Battlefield"],
        correctAnswer: "Valorant",
      ),
      QuestionModel(
        question: "ما هي لعبة التصويب من منظور الشخص الأول التي تدور أحداثها في مدينة 'راپتشر' تحت الماء؟",
        options: ["BioShock", "Half-Life", "Metro: Last Light", "Prey"],
        correctAnswer: "BioShock",
      ),
      QuestionModel(
        question: "أي من ألعاب التصويب التالية تتميز بوجود شخصيات مختلفة ذات قدرات فريدة؟",
        options: ["Counter-Strike: Global Offensive", "Call of Duty: Modern Warfare", "Overwatch", "Battlefield V"],
        correctAnswer: "Overwatch",
      ),
      QuestionModel(
        question: "ما هي لعبة التصويب التي تتضمن عنصراً اسمه 'The Zone' وتقع أحداثها في تشيرنوبيل؟",
        options: ["Metro 2033", "Fallout 3", "S.T.A.L.K.E.R.: Shadow of Chernobyl", "Escape from Tarkov"],
        correctAnswer: "S.T.A.L.K.E.R.: Shadow of Chernobyl",
      ),
    ],
  ),

  // المستوى 9: ألعاب الرعب
  LevelModel(
    level: 9,
    title: "ألعاب الرعب",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFF263238),
      Color(0xFF37474F),
      Color(0xFF455A64),
      Color(0xFF546E7A),
    ],
    questions: [
      QuestionModel(
        question: "أي من الألعاب التالية تعتبر من مؤسسي نوع ألعاب 'الرعب والبقاء' (Survival Horror)؟",
        options: ["Silent Hill", "Resident Evil", "Alone in the Dark", "Fatal Frame"],
        correctAnswer: "Resident Evil",
      ),
      QuestionModel(
        question: "ما هي لعبة الرعب التي تتضمن فندقاً يسمى 'أوفرلوك' وتستند إلى أعمال ستيفن كينغ؟",
        options: ["Alan Wake", "Outlast", "The Evil Within", "The Shining"],
        correctAnswer: "The Shining",
      ),
      QuestionModel(
        question: "ما اسم لعبة الرعب التي تتضمن شخصية رئيسية تدعى 'أيزاك كلارك' وتدور في الفضاء الخارجي؟",
        options: ["Dead Space", "Alien: Isolation", "Prey", "System Shock"],
        correctAnswer: "Dead Space",
      ),
      QuestionModel(
        question: "أي من الألعاب التالية تتضمن مخلوقاً يدعى 'بيراميد هيد'؟",
        options: ["Resident Evil", "Silent Hill", "Fatal Frame", "The Evil Within"],
        correctAnswer: "Silent Hill",
      ),
      QuestionModel(
        question: "ما هي لعبة الرعب التي تتمحور حول الهروب من مستشفى للأمراض النفسية؟",
        options: ["Outlast", "Layers of Fear", "Amnesia: The Dark Descent", "The Evil Within"],
        correctAnswer: "Outlast",
      ),
      QuestionModel(
        question: "ما هي لعبة الرعب التي تجبر اللاعب على مواجهة مخلوق لا يمكن قتله يدعى 'الإكسينومورف'؟",
        options: ["Dead Space", "Alien: Isolation", "Prey", "The Evil Within"],
        correctAnswer: "Alien: Isolation",
      ),
      QuestionModel(
        question: "ما اسم لعبة الرعب التي تتميز بآليات 'الصحة العقلية' وتأثيرها على تجربة اللعب؟",
        options: ["Eternal Darkness: Sanity's Requiem", "Call of Cthulhu", "Silent Hill 2", "Amnesia: The Dark Descent"],
        correctAnswer: "Eternal Darkness: Sanity's Requiem",
      ),
      QuestionModel(
        question: "أي من ألعاب الرعب التالية تتضمن كاميرا للتصوير الفوتوغرافي كسلاح رئيسي؟",
        options: ["Outlast", "Fatal Frame", "Slender", "Layers of Fear"],
        correctAnswer: "Fatal Frame",
      ),
      QuestionModel(
        question: "ما هي لعبة الرعب التي تتضمن كائنات مخيفة تدعى 'نيكرومورف'؟",
        options: ["Resident Evil", "The Evil Within", "Dead Space", "Silent Hill"],
        correctAnswer: "Dead Space",
      ),
      QuestionModel(
        question: "ما هي لعبة الرعب النفسي التي تدور أحداثها في قرية صغيرة وتتضمن قصة شخصية تدعى 'سينوا'؟",
        options: ["Alan Wake", "Hellblade: Senua's Sacrifice", "The Medium", "Blair Witch"],
        correctAnswer: "Hellblade: Senua's Sacrifice",
      ),
    ],
  ),

  // المستوى 10: ثقافة ألعاب الفيديو
  LevelModel(
    level: 10,
    title: "ثقافة ألعاب الفيديو",
    category: QuizCategory.videoGames,
    backgroundGradient: const [
      Color(0xFF3E2723),
      Color(0xFF4E342E),
      Color(0xFF5D4037),
      Color(0xFF6D4C41),
    ],
    questions: [
      QuestionModel(
        question: "ما اسم ظاهرة تتعلق بوجود رسائل أو محتوى سري في ألعاب الفيديو؟",
        options: ["Glitches", "Mods", "Easter Eggs", "Add-ons"],
        correctAnswer: "Easter Eggs",
      ),
      QuestionModel(
        question: "من هو المصمم الياباني المعروف بإنشائه لسلسلة Super Mario وThe Legend of Zelda؟",
        options: ["هيديو كوجيما", "شيغيرو مياموتو", "يوشي ساكاموتو", "هيديكي كامييا"],
        correctAnswer: "شيغيرو مياموتو",
      ),
      QuestionModel(
        question: "ما هو مصطلح 'E3' في صناعة ألعاب الفيديو؟",
        options: [
          "Extreme Entertainment Exhibition",
          "Electronic Entertainment Expo",
          "European Entertainment Exhibition",
          "Elite Entertainment Event"
        ],
        correctAnswer: "Electronic Entertainment Expo",
      ),
      QuestionModel(
        question: "ما هو اسم أول لعبة فيديو تجارية في التاريخ؟",
        options: ["Pong", "Tennis for Two", "Computer Space", "Spacewar!"],
        correctAnswer: "Computer Space",
      ),
      QuestionModel(
        question: "ما هو مصطلح 'AAA' في صناعة ألعاب الفيديو؟",
        options: [
          "American Arcade Association",
          "Advanced Audio Applications",
          "مصطلح يشير إلى الألعاب عالية الميزانية والإنتاج",
          "Arcade Action Adventure"
        ],
        correctAnswer: "مصطلح يشير إلى الألعاب عالية الميزانية والإنتاج",
      ),
      QuestionModel(
        question: "أي شركة ابتكرت محرك لعبة 'Unreal Engine' الشهير؟",
        options: ["Epic Games", "Valve", "EA", "Ubisoft"],
        correctAnswer: "Epic Games",
      ),
      QuestionModel(
        question: "ما هو اسم أول وحدة تحكم أطلقتها شركة Microsoft؟",
        options: ["Xbox", "Xbox 360", "Xbox One", "Xbox Series X"],
        correctAnswer: "Xbox",
      ),
      QuestionModel(
        question: "ما هي الظاهرة المعروفة باسم 'Rage Quitting' في ألعاب الفيديو؟",
        options: [
          "ممارسة التخريب المتعمد للعبة",
          "ترك اللعبة بغضب بسبب الإحباط",
          "محاولة الغش للفوز",
          "اللعب بشكل مكثف لساعات طويلة"
        ],
        correctAnswer: "ترك اللعبة بغضب بسبب الإحباط",
      ),
      QuestionModel(
        question: "ما هو لقب Markus Persson، مبتكر لعبة Minecraft الأصلي؟",
        options: ["Mojang", "Jeb", "Notch", "Herobrine"],
        correctAnswer: "Notch",
      ),
      QuestionModel(
        question: "أي منصة ألعاب فيديو تعتبر الأكثر مبيعًا في التاريخ؟",
        options: ["PlayStation 2", "Nintendo DS", "Game Boy/Game Boy Color", "PlayStation 4"],
        correctAnswer: "PlayStation 2",
      ),
    ],
  ),
];

/// --- 5. قائمة مستويات الرياضة ---
final List<LevelModel> sportsLevels = [
  // المستوى 1: أساسيات كرة القدم
  LevelModel(
    level: 1,
    title: "أساسيات كرة القدم",
    category: QuizCategory.sports,
    isUnlocked: true,
    backgroundGradient: const [
      Color(0xFF1B5E20), // أخضر داكن
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد اللاعبين في فريق كرة القدم على أرض الملعب؟",
        options: ["10", "11", "12", "9"],
        correctAnswer: "11",
      ),
      QuestionModel(
        question: "كم دقيقة مدة مباراة كرة القدم الرسمية؟",
        options: ["80 دقيقة", "90 دقيقة", "100 دقيقة", "120 دقيقة"],
        correctAnswer: "90 دقيقة",
      ),
      QuestionModel(
        question: "ما هو لون البطاقة التي تعني الطرد المباشر؟",
        options: ["الأصفر", "الأحمر", "الأزرق", "الأخضر"],
        correctAnswer: "الأحمر",
      ),
      QuestionModel(
        question: "كم عدد الحكام في مباراة كرة القدم الرسمية؟",
        options: ["1", "2", "3", "4"],
        correctAnswer: "3",
      ),
      QuestionModel(
        question: "ما اسم المنطقة التي يقف فيها حارس المرمى؟",
        options: ["منطقة الجزاء", "منطقة الوسط", "منطقة الركنية", "منطقة التسلل"],
        correctAnswer: "منطقة الجزاء",
      ),
      QuestionModel(
        question: "كم عدد الأشواط في مباراة كرة القدم؟",
        options: ["1", "2", "3", "4"],
        correctAnswer: "2",
      ),
      QuestionModel(
        question: "ما هو عرض مرمى كرة القدم؟",
        options: ["7.32 متر", "8 متر", "6 متر", "9 متر"],
        correctAnswer: "7.32 متر",
      ),
      QuestionModel(
        question: "من أين تُنفذ ضربة الركنية؟",
        options: ["من وسط الملعب", "من زاوية الملعب", "من خط المرمى", "من خط الوسط"],
        correctAnswer: "من زاوية الملعب",
      ),
      QuestionModel(
        question: "ما هو قطر الكرة في كرة القدم تقريباً؟",
        options: ["20 سم", "22 سم", "25 سم", "30 سم"],
        correctAnswer: "22 سم",
      ),
      QuestionModel(
        question: "كم عدد التبديلات المسموحة في المباراة الواحدة؟",
        options: ["3", "5", "7", "لا محدود"],
        correctAnswer: "5",
      ),
    ],
  ),

  // المستوى 2: تاريخ كرة القدم
  LevelModel(
    level: 2,
    title: "تاريخ كرة القدم",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "في أي عام تأسس الاتحاد الدولي لكرة القدم (FIFA)؟",
        options: ["1904", "1900", "1910", "1920"],
        correctAnswer: "1904",
      ),
      QuestionModel(
        question: "أين أقيمت أول بطولة كأس العالم؟",
        options: ["البرازيل", "الأرجنتين", "الأوروغواي", "إيطاليا"],
        correctAnswer: "الأوروغواي",
      ),
      QuestionModel(
        question: "في أي عام أقيمت أول بطولة كأس العالم؟",
        options: ["1928", "1930", "1932", "1934"],
        correctAnswer: "1930",
      ),
      QuestionModel(
        question: "أي دولة فازت بأول كأس عالم؟",
        options: ["البرازيل", "الأرجنتين", "الأوروغواي", "إيطاليا"],
        correctAnswer: "الأوروغواي",
      ),
      QuestionModel(
        question: "في أي دولة نشأت كرة القدم الحديثة؟",
        options: ["البرازيل", "إنجلترا", "ألمانيا", "إسبانيا"],
        correctAnswer: "إنجلترا",
      ),
      QuestionModel(
        question: "متى تأسس أول نادي كرة قدم في التاريخ؟",
        options: ["1857", "1860", "1863", "1870"],
        correctAnswer: "1857",
      ),
      QuestionModel(
        question: "ما اسم أول نادي كرة قدم في التاريخ؟",
        options: ["Sheffield FC", "Manchester United", "Arsenal", "Liverpool"],
        correctAnswer: "Sheffield FC",
      ),
      QuestionModel(
        question: "في أي عام تم إدخال البطاقات الملونة لأول مرة؟",
        options: ["1966", "1970", "1974", "1978"],
        correctAnswer: "1970",
      ),
      QuestionModel(
        question: "أي بطولة كأس عالم شهدت أول استخدام للبطاقات الملونة؟",
        options: ["1966 إنجلترا", "1970 المكسيك", "1974 ألمانيا", "1978 الأرجنتين"],
        correctAnswer: "1970 المكسيك",
      ),
      QuestionModel(
        question: "متى تم تأسيس الاتحاد الإنجليزي لكرة القدم؟",
        options: ["1863", "1865", "1870", "1875"],
        correctAnswer: "1863",
      ),
    ],
  ),

  // المستوى 3: نجوم كرة القدم
  LevelModel(
    level: 3,
    title: "نجوم كرة القدم",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "من هو اللاعب الذي يُلقب بـ 'ملك كرة القدم'؟",
        options: ["مارادونا", "بيليه", "كريستيانو رونالدو", "ميسي"],
        correctAnswer: "بيليه",
      ),
      QuestionModel(
        question: "كم عدد كؤوس العالم التي فاز بها بيليه؟",
        options: ["2", "3", "4", "5"],
        correctAnswer: "3",
      ),
      QuestionModel(
        question: "من هو اللاعب الذي سجل 'هدف القرن' في كأس العالم 1986؟",
        options: ["بيليه", "مارادونا", "رونالدينيو", "زيدان"],
        correctAnswer: "مارادونا",
      ),
      QuestionModel(
        question: "أي لاعب حصل على أكبر عدد من جوائز الكرة الذهبية؟",
        options: ["كريستيانو رونالدو", "ليونيل ميسي", "مارادونا", "بيليه"],
        correctAnswer: "ليونيل ميسي",
      ),
      QuestionModel(
        question: "من هو أكثر اللاعبين تسجيلاً للأهداف في تاريخ كرة القدم؟",
        options: ["بيليه", "كريستيانو رونالدو", "ميسي", "مارادونا"],
        correctAnswer: "كريستيانو رونالدو",
      ),
      QuestionModel(
        question: "أي لاعب يُلقب بـ 'الظاهرة'؟",
        options: ["رونالدو البرازيلي", "كريستيانو رونالدو", "رونالدينيو", "كاكا"],
        correctAnswer: "رونالدو البرازيلي",
      ),
      QuestionModel(
        question: "من هو اللاعب الذي قاد فرنسا للفوز بكأس العالم 1998؟",
        options: ["تييري هنري", "زين الدين زيدان", "ميشيل بلاتيني", "فرانك ريبيري"],
        correctAnswer: "زين الدين زيدان",
      ),
      QuestionModel(
        question: "أي لاعب يُعرف باسم 'الساحر الصغير'؟",
        options: ["ميسي", "مارادونا", "رونالدينيو", "نيمار"],
        correctAnswer: "ميسي",
      ),
      QuestionModel(
        question: "من هو أصغر لاعب سجل في كأس العالم؟",
        options: ["بيليه", "مبابي", "مايكل أوين", "لوكاكو"],
        correctAnswer: "بيليه",
      ),
      QuestionModel(
        question: "أي لاعب حمل الرقم 10 مع الأرجنتين بعد مارادونا؟",
        options: ["ريكيلمي", "ميسي", "أيغواين", "دي ماريا"],
        correctAnswer: "ميسي",
      ),
    ],
  ),

  // المستوى 4: كرة السلة
  LevelModel(
    level: 4,
    title: "كرة السلة",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد اللاعبين في فريق كرة السلة على أرض الملعب؟",
        options: ["4", "5", "6", "7"],
        correctAnswer: "5",
      ),
      QuestionModel(
        question: "كم نقطة يحصل عليها اللاعب عند التسديد من خلف خط الثلاث نقاط؟",
        options: ["2", "3", "4", "5"],
        correctAnswer: "3",
      ),
      QuestionModel(
        question: "كم ارتفاع سلة كرة السلة عن الأرض؟",
        options: ["3 متر", "3.05 متر", "3.5 متر", "4 متر"],
        correctAnswer: "3.05 متر",
      ),
      QuestionModel(
        question: "كم مدة الربع الواحد في كرة السلة (NBA)؟",
        options: ["10 دقائق", "12 دقيقة", "15 دقيقة", "20 دقيقة"],
        correctAnswer: "12 دقيقة",
      ),
      QuestionModel(
        question: "من اخترع لعبة كرة السلة؟",
        options: ["جيمس نايسميث", "والتر كامب", "أبنر دوبلداي", "ويليام مورغان"],
        correctAnswer: "جيمس نايسميث",
      ),
      QuestionModel(
        question: "في أي عام تم اختراع كرة السلة؟",
        options: ["1889", "1891", "1895", "1900"],
        correctAnswer: "1891",
      ),
      QuestionModel(
        question: "كم عدد الأرباع في مباراة كرة السلة؟",
        options: ["2", "3", "4", "5"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "ما هو اسم أشهر دوري كرة سلة في العالم؟",
        options: ["NBA", "EuroLeague", "CBA", "NBL"],
        correctAnswer: "NBA",
      ),
      QuestionModel(
        question: "كم عدد اللاعبين الاحتياط المسموح بهم في فريق كرة السلة؟",
        options: ["5", "7", "8", "12"],
        correctAnswer: "7",
      ),
      QuestionModel(
        question: "ما هو قطر كرة السلة تقريباً؟",
        options: ["22 سم", "24 سم", "26 سم", "28 سم"],
        correctAnswer: "24 سم",
      ),
    ],
  ),

  // المستوى 5: التنس
  LevelModel(
    level: 5,
    title: "التنس",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد البطولات الكبرى في التنس؟",
        options: ["3", "4", "5", "6"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "ما هي أقدم بطولة تنس في العالم؟",
        options: ["ويمبلدون", "الولايات المتحدة المفتوحة", "فرنسا المفتوحة", "أستراليا المفتوحة"],
        correctAnswer: "ويمبلدون",
      ),
      QuestionModel(
        question: "على أي سطح تُلعب بطولة فرنسا المفتوحة؟",
        options: ["العشب", "الطين", "الصلب", "السجاد"],
        correctAnswer: "الطين",
      ),
      QuestionModel(
        question: "كم عدد الأشواط المطلوبة للفوز في مباراة التنس للرجال في البطولات الكبرى؟",
        options: ["2", "3", "4", "5"],
        correctAnswer: "3",
      ),
      QuestionModel(
        question: "ما هو أقصى عدد نقاط في الشوط الواحد؟",
        options: ["4", "6", "7", "لا يوجد حد أقصى"],
        correctAnswer: "لا يوجد حد أقصى",
      ),
      QuestionModel(
        question: "كم نقطة يحتاجها اللاعب للفوز بالمجموعة؟",
        options: ["4", "6", "7", "8"],
        correctAnswer: "6",
      ),
      QuestionModel(
        question: "ما اسم النقطة التي تساوي صفر في التنس؟",
        options: ["Zero", "Love", "Nil", "Nothing"],
        correctAnswer: "Love",
      ),
      QuestionModel(
        question: "كم يبلغ طول ملعب التنس؟",
        options: ["23.77 متر", "25 متر", "26.5 متر", "28 متر"],
        correctAnswer: "23.77 متر",
      ),
      QuestionModel(
        question: "ما هو ارتفاع الشبكة في وسط الملعب؟",
        options: ["0.91 متر", "1 متر", "1.07 متر", "1.2 متر"],
        correctAnswer: "0.91 متر",
      ),
      QuestionModel(
        question: "من هو اللاعب الذي حقق أكبر عدد من البطولات الكبرى للرجال؟",
        options: ["روجر فيدرر", "رافائيل نادال", "نوفاك ديوكوفيتش", "بيت سامبراس"],
        correctAnswer: "نوفاك ديوكوفيتش",
      ),
    ],
  ),

  // المستوى 6: السباحة
  LevelModel(
    level: 6,
    title: "السباحة",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد أنواع السباحة الرسمية في الألعاب الأولمبية؟",
        options: ["3", "4", "5", "6"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "ما هي أسرع طريقة سباحة؟",
        options: ["الحرة", "الظهر", "الصدر", "الفراشة"],
        correctAnswer: "الحرة",
      ),
      QuestionModel(
        question: "كم يبلغ طول المسبح الأولمبي؟",
        options: ["25 متر", "50 متر", "75 متر", "100 متر"],
        correctAnswer: "50 متر",
      ),
      QuestionModel(
        question: "كم عدد المسارات في المسبح الأولمبي؟",
        options: ["6", "8", "10", "12"],
        correctAnswer: "8",
      ),
      QuestionModel(
        question: "ما هو عمق المسبح الأولمبي الأدنى؟",
        options: ["1.5 متر", "2 متر", "2.5 متر", "3 متر"],
        correctAnswer: "2 متر",
      ),
      QuestionModel(
        question: "في أي سباحة يبدأ السباح من داخل الماء؟",
        options: ["الحرة", "الظهر", "الصدر", "الفراشة"],
        correctAnswer: "الظهر",
      ),
      QuestionModel(
        question: "من هو السباح الذي حقق أكبر عدد من الميداليات الأولمبية؟",
        options: ["مايكل فيلبس", "مارك سبيتز", "كاتي ليديكي", "آدم بيتي"],
        correctAnswer: "مايكل فيلبس",
      ),
      QuestionModel(
        question: "كم ميدالية ذهبية أولمبية حقق مايكل فيلبس؟",
        options: ["20", "23", "25", "28"],
        correctAnswer: "23",
      ),
      QuestionModel(
        question: "ما هي المسافة الأقصر في سباقات السباحة الأولمبية؟",
        options: ["25 متر", "50 متر", "100 متر", "200 متر"],
        correctAnswer: "50 متر",
      ),
      QuestionModel(
        question: "في أي عام دخلت السباحة للنساء الألعاب الأولمبية؟",
        options: ["1900", "1912", "1920", "1924"],
        correctAnswer: "1912",
      ),
    ],
  ),

  // المستوى 7: ألعاب القوى
  LevelModel(
    level: 7,
    title: "ألعاب القوى",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "كم يبلغ طول مضمار الجري الأولمبي؟",
        options: ["200 متر", "300 متر", "400 متر", "500 متر"],
        correctAnswer: "400 متر",
      ),
      QuestionModel(
        question: "كم عدد الحواجز في سباق 110 متر حواجز للرجال؟",
        options: ["8", "10", "12", "15"],
        correctAnswer: "10",
      ),
      QuestionModel(
        question: "ما هو أطول سباق في ألعاب القوى الأولمبية؟",
        options: ["10000 متر", "الماراثون", "20 كم مشي", "50 كم مشي"],
        correctAnswer: "50 كم مشي",
      ),
      QuestionModel(
        question: "كم تبلغ مسافة الماراثون؟",
        options: ["40 كم", "42.195 كم", "45 كم", "50 كم"],
        correctAnswer: "42.195 كم",
      ),
      QuestionModel(
        question: "من هو أسرع رجل في العالم حالياً؟",
        options: ["يوسين بولت", "تايسون غاي", "جاستن غاتلين", "نوا لايلز"],
        correctAnswer: "يوسين بولت",
      ),
      QuestionModel(
        question: "كم يبلغ الرقم القياسي العالمي في 100 متر للرجال؟",
        options: ["9.58 ثانية", "9.63 ثانية", "9.69 ثانية", "9.72 ثانية"],
        correctAnswer: "9.58 ثانية",
      ),
      QuestionModel(
        question: "كم عدد الأحداث في مسابقة العشاري للرجال؟",
        options: ["8", "10", "12", "15"],
        correctAnswer: "10",
      ),
      QuestionModel(
        question: "ما هو وزن الجلة في مسابقة دفع الجلة للرجال؟",
        options: ["6 كغ", "7.26 كغ", "8 كغ", "10 كغ"],
        correctAnswer: "7.26 كغ",
      ),
      QuestionModel(
        question: "كم يبلغ ارتفاع الحاجز في سباق 110 متر حواجز للرجال؟",
        options: ["0.84 متر", "0.91 متر", "1.067 متر", "1.2 متر"],
        correctAnswer: "1.067 متر",
      ),
      QuestionModel(
        question: "في أي مدينة أقيمت أول ألعاب أولمبية حديثة؟",
        options: ["لندن", "باريس", "أثينا", "روما"],
        correctAnswer: "أثينا",
      ),
    ],
  ),

  // المستوى 8: الجمباز
  LevelModel(
    level: 8,
    title: "الجمباز",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد الأجهزة في الجمباز الفني للرجال؟",
        options: ["4", "6", "8", "10"],
        correctAnswer: "6",
      ),
      QuestionModel(
        question: "كم عدد الأجهزة في الجمباز الفني للنساء؟",
        options: ["2", "4", "6", "8"],
        correctAnswer: "4",
      ),
      QuestionModel(
        question: "ما هو أعلى تقييم ممكن في الجمباز؟",
        options: ["10", "15", "20", "لا يوجد حد أقصى"],
        correctAnswer: "لا يوجد حد أقصى",
      ),
      QuestionModel(
        question: "أي من هذه الأجهزة خاص بالنساء فقط؟",
        options: ["المتوازي", "العقلة", "عارضة التوازن", "الحلق"],
        correctAnswer: "عارضة التوازن",
      ),
      QuestionModel(
        question: "كم يبلغ عرض عارضة التوازن؟",
        options: ["8 سم", "10 سم", "12 سم", "15 سم"],
        correctAnswer: "10 سم",
      ),
      QuestionModel(
        question: "من هي الجمبازية التي حققت أكبر عدد من الميداليات الأولمبية؟",
        options: ["نادية كومانيتشي", "شانون ميلر", "لاريسا لاتينينا", "سيمون بايلز"],
        correctAnswer: "لاريسا لاتينينا",
      ),
      QuestionModel(
        question: "في أي عام حققت نادية كومانيتشي أول 10 مثالي في التاريخ الأولمبي؟",
        options: ["1972", "1976", "1980", "1984"],
        correctAnswer: "1976",
      ),
      QuestionModel(
        question: "كم يبلغ ارتفاع عارضة التوازن عن الأرض؟",
        options: ["1.25 متر", "1.5 متر", "1.75 متر", "2 متر"],
        correctAnswer: "1.25 متر",
      ),
      QuestionModel(
        question: "ما هو طول عارضة التوازن؟",
        options: ["3 متر", "4 متر", "5 متر", "6 متر"],
        correctAnswer: "5 متر",
      ),
      QuestionModel(
        question: "أي من هذه الأجهزة خاص بالرجال فقط؟",
        options: ["الحصان", "الحلق", "الأرضي", "القفز"],
        correctAnswer: "الحلق",
      ),
    ],
  ),

  // المستوى 9: الملاكمة
  LevelModel(
    level: 9,
    title: "الملاكمة",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد الجولات في مباراة الملاكمة المحترفة للوزن الثقيل؟",
        options: ["10", "12", "15", "20"],
        correctAnswer: "12",
      ),
      QuestionModel(
        question: "كم تبلغ مدة الجولة الواحدة في الملاكمة المحترفة؟",
        options: ["2 دقيقة", "3 دقائق", "4 دقائق", "5 دقائق"],
        correctAnswer: "3 دقائق",
      ),
      QuestionModel(
        question: "من هو الملاكم الذي يُلقب بـ 'الأعظم'؟",
        options: ["مايك تايسون", "محمد علي", "جو فريزر", "جورج فورمان"],
        correctAnswer: "محمد علي",
      ),
      QuestionModel(
        question: "كم عدد فئات الوزن في الملاكمة المحترفة؟",
        options: ["12", "15", "17", "20"],
        correctAnswer: "17",
      ),
      QuestionModel(
        question: "ما هو أخف وزن في الملاكمة المحترفة؟",
        options: ["الوزن الخفيف", "وزن الذبابة الصغير", "وزن القش", "الوزن الذري"],
        correctAnswer: "وزن القش",
      ),
      QuestionModel(
        question: "من هو أصغر بطل عالمي في تاريخ الملاكمة للوزن الثقيل؟",
        options: ["محمد علي", "مايك تايسون", "إيفاندر هوليفيلد", "لينوكس لويس"],
        correctAnswer: "مايك تايسون",
      ),
      QuestionModel(
        question: "كم كان عمر مايك تايسون عندما أصبح بطل العالم للوزن الثقيل؟",
        options: ["19 سنة", "20 سنة", "21 سنة", "22 سنة"],
        correctAnswer: "20 سنة",
      ),
      QuestionModel(
        question: "ما هو اسم الحلبة الشهيرة في نيويورك؟",
        options: ["ماديسون سكوير غاردن", "يانكي ستاديوم", "بروكلين نتس", "بارك أفينيو"],
        correctAnswer: "ماديسون سكوير غاردن",
      ),
      QuestionModel(
        question: "كم عدد الحكام في مباراة الملاكمة المحترفة؟",
        options: ["1", "3", "5", "7"],
        correctAnswer: "3",
      ),
      QuestionModel(
        question: "ما هو الحد الأقصى لوزن الملاكم في فئة الوزن الثقيل؟",
        options: ["90 كغ", "100 كغ", "لا يوجد حد أقصى", "120 كغ"],
        correctAnswer: "لا يوجد حد أقصى",
      ),
    ],
  ),

  // المستوى 10: الرياضات المتنوعة (الأصعب)
  LevelModel(
    level: 10,
    title: "الرياضات المتنوعة",
    category: QuizCategory.sports,
    backgroundGradient: const [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF43A047),
      Color(0xFF66BB6A),
    ],
    questions: [
      QuestionModel(
        question: "في أي عام أقيمت أول ألعاب أولمبية شتوية؟",
        options: ["1920", "1924", "1928", "1932"],
        correctAnswer: "1924",
      ),
      QuestionModel(
        question: "أي دولة استضافت أكبر عدد من الألعاب الأولمبية الصيفية؟",
        options: ["الولايات المتحدة", "فرنسا", "بريطانيا", "اليونان"],
        correctAnswer: "الولايات المتحدة",
      ),
      QuestionModel(
        question: "ما هو الرقم القياسي العالمي في الوثب العالي للرجال؟",
        options: ["2.40 متر", "2.45 متر", "2.50 متر", "2.55 متر"],
        correctAnswer: "2.45 متر",
      ),
      QuestionModel(
        question: "من هو صاحب الرقم القياسي العالمي في الوثب العالي للرجال؟",
        options: ["خافيير سوتومايور", "موتاز عيسى برشم", "ستيفان هولم", "باتريك شوبرغ"],
        correctAnswer: "خافيير سوتومايور",
      ),
      QuestionModel(
        question: "كم عدد الرياضات في الألعاب الأولمبية الصيفية 2024؟",
        options: ["28", "32", "35", "40"],
        correctAnswer: "32",
      ),
      QuestionModel(
        question: "أي رياضة تُعرف باسم 'الرياضة الملكية'؟",
        options: ["التنس", "الجولف", "سباق الخيل", "البولو"],
        correctAnswer: "سباق الخيل",
      ),
      QuestionModel(
        question: "في أي رياضة يُستخدم مصطلح 'هول إن وان'؟",
        options: ["التنس", "الجولف", "البيسبول", "الكريكت"],
        correctAnswer: "الجولف",
      ),
      QuestionModel(
        question: "كم عدد اللاعبين في فريق الرجبي؟",
        options: ["13", "15", "17", "20"],
        correctAnswer: "15",
      ),
      QuestionModel(
        question: "أي دولة فازت بأكبر عدد من كؤوس العالم لكرة القدم؟",
        options: ["الأرجنتين", "ألمانيا", "البرازيل", "إيطاليا"],
        correctAnswer: "البرازيل",
      ),
      QuestionModel(
        question: "ما هو أطول سباق في الألعاب الأولمبية؟",
        options: ["الماراثون", "50 كم مشي", "20 كم مشي", "10000 متر"],
        correctAnswer: "50 كم مشي",
      ),
    ],
  ),
];

/// --- 6. قائمة مستويات الأدب والشعر ---
final List<LevelModel> literatureLevels = [
  // المستوى 1: أساسيات الشعر العربي
  LevelModel(
    level: 1,
    title: "أساسيات الشعر العربي",
    category: QuizCategory.literature,
    isUnlocked: true,
    backgroundGradient: const [
      Color(0xFF4A148C), // بنفسجي داكن
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد بحور الشعر العربي؟",
        options: ["14", "15", "16", "17"],
        correctAnswer: "16",
      ),
      QuestionModel(
        question: "من هو أمير الشعراء؟",
        options: ["أحمد شوقي", "حافظ إبراهيم", "المتنبي", "امرؤ القيس"],
        correctAnswer: "أحمد شوقي",
      ),
      QuestionModel(
        question: "ما هو أشهر بحور الشعر العربي؟",
        options: ["الطويل", "البسيط", "الكامل", "الوافر"],
        correctAnswer: "الطويل",
      ),
      QuestionModel(
        question: "كم عدد أبيات المعلقة عادة؟",
        options: ["50-70", "70-100", "100-150", "أكثر من 150"],
        correctAnswer: "70-100",
      ),
      QuestionModel(
        question: "من صاحب معلقة 'قفا نبك من ذكرى حبيب ومنزل'؟",
        options: ["امرؤ القيس", "طرفة بن العبد", "زهير بن أبي سلمى", "عنترة بن شداد"],
        correctAnswer: "امرؤ القيس",
      ),
      QuestionModel(
        question: "ما هو الغرض الشعري الذي يتحدث عن الحب؟",
        options: ["الهجاء", "المدح", "الغزل", "الرثاء"],
        correctAnswer: "الغزل",
      ),
      QuestionModel(
        question: "من هو شاعر الرسول صلى الله عليه وسلم؟",
        options: ["حسان بن ثابت", "كعب بن زهير", "عبد الله بن رواحة", "النابغة الذبياني"],
        correctAnswer: "حسان بن ثابت",
      ),
      QuestionModel(
        question: "ما اسم القصيدة التي تنتهي جميع أبياتها بنفس الحرف؟",
        options: ["المقطوعة", "القصيدة الموحدة", "القصيدة المقفاة", "الأرجوزة"],
        correctAnswer: "القصيدة المقفاة",
      ),
      QuestionModel(
        question: "من هو صاحب ديوان 'الشوقيات'؟",
        options: ["أحمد شوقي", "حافظ إبراهيم", "إيليا أبو ماضي", "جبران خليل جبران"],
        correctAnswer: "أحمد شوقي",
      ),
      QuestionModel(
        question: "ما هو العصر الذي ازدهر فيه الشعر العربي أكثر؟",
        options: ["الجاهلي", "الإسلامي", "الأموي", "العباسي"],
        correctAnswer: "العباسي",
      ),
    ],
  ),

  // المستوى 2: شعراء الجاهلية
  LevelModel(
    level: 2,
    title: "شعراء الجاهلية",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "من هو صاحب معلقة 'لخولة أطلال ببرقة ثهمد'؟",
        options: ["طرفة بن العبد", "عنترة بن شداد", "الحارث بن حلزة", "امرؤ القيس"],
        correctAnswer: "طرفة بن العبد",
      ),
      QuestionModel(
        question: "كم عدد المعلقات المشهورة؟",
        options: ["5", "7", "10", "12"],
        correctAnswer: "7",
      ),
      QuestionModel(
        question: "من هو الشاعر الذي قُتل وهو شاب؟",
        options: ["طرفة بن العبد", "عنترة بن شداد", "امرؤ القيس", "لبيد بن ربيعة"],
        correctAnswer: "طرفة بن العبد",
      ),
      QuestionModel(
        question: "من هو الشاعر الفارس الذي اشتهر بحبه لعبلة؟",
        options: ["عنترة بن شداد", "امرؤ القيس", "طرفة بن العبد", "زهير بن أبي سلمى"],
        correctAnswer: "عنترة بن شداد",
      ),
      QuestionModel(
        question: "من هو آخر شعراء الجاهلية؟",
        options: ["لبيد بن ربيعة", "النابغة الذبياني", "الأعشى", "زهير بن أبي سلمى"],
        correctAnswer: "لبيد بن ربيعة",
      ),
      QuestionModel(
        question: "من هو صاحب البيت: 'وما المال والأهلون إلا ودائع'؟",
        options: ["زهير بن أبي سلمى", "لبيد بن ربيعة", "طرفة بن العبد", "عمرو بن كلثوم"],
        correctAnswer: "زهير بن أبي سلمى",
      ),
      QuestionModel(
        question: "أي شاعر جاهلي كان يُلقب بـ'صناجة العرب'؟",
        options: ["الأعشى", "النابغة", "طرفة", "عنترة"],
        correctAnswer: "الأعشى",
      ),
      QuestionModel(
        question: "من هو الشاعر الذي قال: 'بلغنا السماء مجدنا وجدودنا'؟",
        options: ["عمرو بن كلثوم", "الحارث بن حلزة", "طرفة بن العبد", "عنترة بن شداد"],
        correctAnswer: "عمرو بن كلثوم",
      ),
      QuestionModel(
        question: "في أي مكان كانت تُعلق المعلقات؟",
        options: ["الكعبة", "سوق عكاظ", "دار الندوة", "جميع ما سبق"],
        correctAnswer: "الكعبة",
      ),
      QuestionModel(
        question: "من هو الشاعر الذي عاش 300 سنة؟",
        options: ["لبيد بن ربيعة", "النابغة الذبياني", "الأعشى", "زهير بن أبي سلمى"],
        correctAnswer: "لبيد بن ربيعة",
      ),
    ],
  ),

  // المستوى 3: الشعر في العصر الإسلامي
  LevelModel(
    level: 3,
    title: "الشعر الإسلامي",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "من هو شاعر الرسول الأشهر؟",
        options: ["حسان بن ثابت", "كعب بن زهير", "عبد الله بن رواحة", "كعب بن مالك"],
        correctAnswer: "حسان بن ثابت",
      ),
      QuestionModel(
        question: "من صاحب قصيدة 'بانت سعاد'؟",
        options: ["كعب بن زهير", "حسان بن ثابت", "النابغة الجعدي", "لبيد بن ربيعة"],
        correctAnswer: "كعب بن زهير",
      ),
      QuestionModel(
        question: "ما هو لقب حسان بن ثابت؟",
        options: ["شاعر الرسول", "شاعر المدينة", "شاعر الأنصار", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "من هو الشاعر الذي أسلم بعد أن هجا الرسول؟",
        options: ["كعب بن زهير", "النابغة الجعدي", "الأعشى", "لبيد بن ربيعة"],
        correctAnswer: "كعب بن زهير",
      ),
      QuestionModel(
        question: "أي شاعر من شعراء الرسول استشهد في غزوة مؤتة؟",
        options: ["عبد الله بن رواحة", "حسان بن ثابت", "كعب بن مالك", "ثابت بن قيس"],
        correctAnswer: "عبد الله بن رواحة",
      ),
      QuestionModel(
        question: "ما هو الغرض الشعري الذي ازدهر في العصر الإسلامي؟",
        options: ["المدح النبوي", "الغزل", "الهجاء", "الوصف"],
        correctAnswer: "المدح النبوي",
      ),
      QuestionModel(
        question: "من هو الشاعر الذي قال: 'هجوت محمداً فأجبت عنه'؟",
        options: ["حسان بن ثابت", "كعب بن زهير", "عبد الله بن رواحة", "النابغة الجعدي"],
        correctAnswer: "حسان بن ثابت",
      ),
      QuestionModel(
        question: "أي شاعر جاهلي أسلم وعاش في الإسلام طويلاً؟",
        options: ["لبيد بن ربيعة", "النابغة الذبياني", "الأعشى", "عنترة بن شداد"],
        correctAnswer: "لبيد بن ربيعة",
      ),
      QuestionModel(
        question: "ما هو اسم القبيلة التي ينتمي إليها حسان بن ثابت؟",
        options: ["الخزرج", "الأوس", "قريش", "تميم"],
        correctAnswer: "الخزرج",
      ),
      QuestionModel(
        question: "من هو الشاعر الذي كان يُلقب بـ'ترجمان القرآن'؟",
        options: ["عبد الله بن عباس", "حسان بن ثابت", "كعب بن زهير", "عبد الله بن رواحة"],
        correctAnswer: "عبد الله بن عباس",
      ),
    ],
  ),

  // المستوى 4: الشعر العباسي
  LevelModel(
    level: 4,
    title: "الشعر العباسي",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "من هو أعظم شعراء العصر العباسي؟",
        options: ["المتنبي", "أبو تمام", "البحتري", "أبو نواس"],
        correctAnswer: "المتنبي",
      ),
      QuestionModel(
        question: "من صاحب البيت: 'على قدر أهل العزم تأتي العزائم'؟",
        options: ["المتنبي", "أبو تمام", "البحتري", "المعري"],
        correctAnswer: "المتنبي",
      ),
      QuestionModel(
        question: "من هو شاعر الخمر في العصر العباسي؟",
        options: ["أبو نواس", "بشار بن برد", "أبو تمام", "البحتري"],
        correctAnswer: "أبو نواس",
      ),
      QuestionModel(
        question: "من هو صاحب ديوان 'لزوم ما لا يلزم'؟",
        options: ["أبو العلاء المعري", "المتنبي", "أبو تمام", "البحتري"],
        correctAnswer: "أبو العلاء المعري",
      ),
      QuestionModel(
        question: "أي شاعر عباسي كان أعمى؟",
        options: ["بشار بن برد", "أبو العلاء المعري", "كلاهما", "لا أحد منهما"],
        correctAnswer: "كلاهما",
      ),
      QuestionModel(
        question: "من هو صاحب قصيدة 'البردة' الشهيرة؟",
        options: ["البوصيري", "ابن الفارض", "الشريف الرضي", "ابن زيدون"],
        correctAnswer: "البوصيري",
      ),
      QuestionModel(
        question: "من هو الشاعر الذي لُقب بـ'شاعر السيف والقلم'؟",
        options: ["المتنبي", "أبو تمام", "البحتري", "ابن المعتز"],
        correctAnswer: "المتنبي",
      ),
      QuestionModel(
        question: "أي شاعر عباسي اشتهر بالحكمة والفلسفة؟",
        options: ["أبو العلاء المعري", "المتنبي", "أبو تمام", "البحتري"],
        correctAnswer: "أبو العلاء المعري",
      ),
      QuestionModel(
        question: "من هو صاحب البيت: 'دع الأيام تفعل ما تشاء'؟",
        options: ["الإمام الشافعي", "أبو العلاء المعري", "المتنبي", "أبو تمام"],
        correctAnswer: "الإمام الشافعي",
      ),
      QuestionModel(
        question: "أي شاعر عباسي كان يُلقب بـ'أبو الطيب'؟",
        options: ["المتنبي", "أبو تمام", "البحتري", "أبو نواس"],
        correctAnswer: "المتنبي",
      ),
    ],
  ),

  // المستوى 5: الأدب الأندلسي
  LevelModel(
    level: 5,
    title: "الأدب الأندلسي",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "من هو أشهر شعراء الأندلس؟",
        options: ["ابن زيدون", "ابن هانئ", "ابن خفاجة", "المعتمد بن عباد"],
        correctAnswer: "ابن زيدون",
      ),
      QuestionModel(
        question: "من هي الشاعرة التي أحبها ابن زيدون؟",
        options: ["ولادة بنت المستكفي", "عائشة القرطبية", "حفصة الركونية", "مريم الأندلسية"],
        correctAnswer: "ولادة بنت المستكفي",
      ),
      QuestionModel(
        question: "ما هو الفن الشعري الذي ازدهر في الأندلس؟",
        options: ["الموشحات", "الزجل", "كلاهما", "لا شيء مما سبق"],
        correctAnswer: "كلاهما",
      ),
      QuestionModel(
        question: "من هو صاحب موشحة 'جادك الغيث'؟",
        options: ["لسان الدين بن الخطيب", "ابن زيدون", "ابن هانئ", "المعتمد بن عباد"],
        correctAnswer: "لسان الدين بن الخطيب",
      ),
      QuestionModel(
        question: "أي شاعر أندلسي كان ملكاً؟",
        options: ["المعتمد بن عباد", "ابن زيدون", "ابن هانئ", "ابن خفاجة"],
        correctAnswer: "المعتمد بن عباد",
      ),
      QuestionModel(
        question: "من هو صاحب قصيدة 'نونية ابن زيدون'؟",
        options: ["ابن زيدون", "ابن هانئ", "المعتمد بن عباد", "لسان الدين بن الخطيب"],
        correctAnswer: "ابن زيدون",
      ),
      QuestionModel(
        question: "ما هو اسم القصر الذي وصفه ابن زيدون في شعره؟",
        options: ["الزهراء", "الحمراء", "قرطبة", "إشبيلية"],
        correctAnswer: "الزهراء",
      ),
      QuestionModel(
        question: "من هو شاعر الطبيعة في الأندلس؟",
        options: ["ابن خفاجة", "ابن زيدون", "ابن هانئ", "المعتمد بن عباد"],
        correctAnswer: "ابن خفاجة",
      ),
      QuestionModel(
        question: "أي مدينة أندلسية كانت مركز الثقافة والأدب؟",
        options: ["قرطبة", "إشبيلية", "غرناطة", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "من هو آخر شعراء الأندلس؟",
        options: ["لسان الدين بن الخطيب", "ابن زمرك", "أبو البقاء الرندي", "ابن الأبار"],
        correctAnswer: "أبو البقاء الرندي",
      ),
    ],
  ),

  // المستوى 6: الشعر الحديث
  LevelModel(
    level: 6,
    title: "الشعر الحديث",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "من هو رائد الشعر الحديث؟",
        options: ["أحمد شوقي", "حافظ إبراهيم", "خليل مطران", "إيليا أبو ماضي"],
        correctAnswer: "خليل مطران",
      ),
      QuestionModel(
        question: "من هو صاحب قصيدة 'نهج البردة'؟",
        options: ["أحمد شوقي", "حافظ إبراهيم", "خليل مطران", "معروف الرصافي"],
        correctAnswer: "أحمد شوقي",
      ),
      QuestionModel(
        question: "من يُلقب بـ'شاعر النيل'؟",
        options: ["حافظ إبراهيم", "أحمد شوقي", "خليل مطران", "عبد الرحمن شكري"],
        correctAnswer: "حافظ إبراهيم",
      ),
      QuestionModel(
        question: "من هو مؤسس مدرسة الديوان؟",
        options: ["عبد الرحمن شكري", "إبراهيم المازني", "عباس العقاد", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "من هو صاحب ديوان 'أوراق الخريف'؟",
        options: ["إبراهيم ناجي", "علي محمود طه", "أحمد زكي أبو شادي", "صالح جودت"],
        correctAnswer: "إبراهيم ناجي",
      ),
      QuestionModel(
        question: "أي شاعر لبناني هاجر إلى أمريكا؟",
        options: ["إيليا أبو ماضي", "جبران خليل جبران", "ميخائيل نعيمة", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "من هو صاحب قصيدة 'الطلاسم'؟",
        options: ["إيليا أبو ماضي", "جبران خليل جبران", "ميخائيل نعيمة", "أمين الريحاني"],
        correctAnswer: "إيليا أبو ماضي",
      ),
      QuestionModel(
        question: "ما اسم الجماعة الأدبية التي أسسها شعراء المهجر؟",
        options: ["الرابطة القلمية", "العصبة الأندلسية", "جماعة أبولو", "مدرسة الديوان"],
        correctAnswer: "الرابطة القلمية",
      ),
      QuestionModel(
        question: "من هو شاعر الشباب؟",
        options: ["أحمد رامي", "علي محمود طه", "إبراهيم ناجي", "صالح جودت"],
        correctAnswer: "علي محمود طه",
      ),
      QuestionModel(
        question: "من هو صاحب قصيدة 'الأطلال'؟",
        options: ["إبراهيم ناجي", "أحمد شوقي", "حافظ إبراهيم", "علي محمود طه"],
        correctAnswer: "إبراهيم ناجي",
      ),
    ],
  ),

  // المستوى 7: النثر العربي
  LevelModel(
    level: 7,
    title: "النثر العربي",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "من هو أشهر كتاب النثر في العصر العباسي؟",
        options: ["الجاحظ", "ابن المقفع", "أبو حيان التوحيدي", "الهمذاني"],
        correctAnswer: "الجاحظ",
      ),
      QuestionModel(
        question: "ما هو أشهر كتب الجاحظ؟",
        options: ["البخلاء", "الحيوان", "البيان والتبيين", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "من هو مؤسس فن المقامات؟",
        options: ["بديع الزمان الهمذاني", "الحريري", "الجاحظ", "ابن المقفع"],
        correctAnswer: "بديع الزمان الهمذاني",
      ),
      QuestionModel(
        question: "من هو صاحب 'مقامات الحريري'؟",
        options: ["أبو محمد الحريري", "بديع الزمان الهمذاني", "الجاحظ", "ابن المقفع"],
        correctAnswer: "أبو محمد الحريري",
      ),
      QuestionModel(
        question: "من هو مترجم كتاب 'كليلة ودمنة'؟",
        options: ["ابن المقفع", "الجاحظ", "ابن قتيبة", "الهمذاني"],
        correctAnswer: "ابن المقفع",
      ),
      QuestionModel(
        question: "ما هو أصل كتاب 'كليلة ودمنة'؟",
        options: ["هندي", "فارسي", "يوناني", "عربي"],
        correctAnswer: "هندي",
      ),
      QuestionModel(
        question: "من هو صاحب كتاب 'الإمتاع والمؤانسة'؟",
        options: ["أبو حيان التوحيدي", "الجاحظ", "ابن المقفع", "الهمذاني"],
        correctAnswer: "أبو حيان التوحيدي",
      ),
      QuestionModel(
        question: "أي نوع من النثر ازدهر في العصر الأموي؟",
        options: ["الخطابة", "الرسائل", "القصص", "المقامات"],
        correctAnswer: "الخطابة",
      ),
      QuestionModel(
        question: "من هو أشهر خطباء العرب؟",
        options: ["قس بن ساعدة", "سحبان وائل", "الحجاج بن يوسف", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "ما هو اسم بطل مقامات الهمذاني؟",
        options: ["أبو الفتح الإسكندري", "أبو زيد السروجي", "عيسى بن هشام", "الحارث بن همام"],
        correctAnswer: "أبو الفتح الإسكندري",
      ),
    ],
  ),

  // المستوى 8: الأدب المعاصر
  LevelModel(
    level: 8,
    title: "الأدب المعاصر",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "من هو رائد الرواية العربية؟",
        options: ["نجيب محفوظ", "توفيق الحكيم", "طه حسين", "محمد حسين هيكل"],
        correctAnswer: "محمد حسين هيكل",
      ),
      QuestionModel(
        question: "من هو أول عربي حصل على جائزة نوبل للآداب؟",
        options: ["نجيب محفوظ", "توفيق الحكيم", "طه حسين", "أحمد شوقي"],
        correctAnswer: "نجيب محفوظ",
      ),
      QuestionModel(
        question: "ما هي أول رواية عربية؟",
        options: ["زينب", "عودة الروح", "دعاء الكروان", "الأيام"],
        correctAnswer: "زينب",
      ),
      QuestionModel(
        question: "من هو صاحب رواية 'الثلاثية'؟",
        options: ["نجيب محفوظ", "توفيق الحكيم", "يحيى حقي", "عبد الرحمن الشرقاوي"],
        correctAnswer: "نجيب محفوظ",
      ),
      QuestionModel(
        question: "من هو عميد الأدب العربي؟",
        options: ["طه حسين", "العقاد", "المازني", "شكري"],
        correctAnswer: "طه حسين",
      ),
      QuestionModel(
        question: "ما هو اسم السيرة الذاتية لطه حسين؟",
        options: ["الأيام", "في الشعر الجاهلي", "مستقبل الثقافة", "حديث الأربعاء"],
        correctAnswer: "الأيام",
      ),
      QuestionModel(
        question: "من هو صاحب رواية 'مدن الملح'؟",
        options: ["عبد الرحمن منيف", "غازي القصيبي", "تركي الحمد", "يوسف السباعي"],
        correctAnswer: "عبد الرحمن منيف",
      ),
      QuestionModel(
        question: "من هي أول روائية عربية؟",
        options: ["زينب فواز", "مي زيادة", "عائشة التيمورية", "وردة اليازجي"],
        correctAnswer: "زينب فواز",
      ),
      QuestionModel(
        question: "من هو صاحب رواية 'موسم الهجرة إلى الشمال'؟",
        options: ["الطيب صالح", "عبد الرحمن منيف", "حنا مينة", "غائب طعمة فرمان"],
        correctAnswer: "الطيب صالح",
      ),
      QuestionModel(
        question: "أي كاتب عربي اشتهر بالمسرح؟",
        options: ["توفيق الحكيم", "يوسف إدريس", "سعد الله ونوس", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
    ],
  ),

  // المستوى 9: البلاغة والنقد
  LevelModel(
    level: 9,
    title: "البلاغة والنقد",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "كم عدد علوم البلاغة؟",
        options: ["2", "3", "4", "5"],
        correctAnswer: "3",
      ),
      QuestionModel(
        question: "ما هي علوم البلاغة الثلاثة؟",
        options: ["المعاني والبيان والبديع", "النحو والصرف والبلاغة", "الشعر والنثر والخطابة", "التشبيه والاستعارة والكناية"],
        correctAnswer: "المعاني والبيان والبديع",
      ),
      QuestionModel(
        question: "من هو واضع علم البلاغة؟",
        options: ["عبد القاهر الجرجاني", "الجاحظ", "ابن المعتز", "السكاكي"],
        correctAnswer: "عبد القاهر الجرجاني",
      ),
      QuestionModel(
        question: "ما هو أشهر كتب البلاغة؟",
        options: ["دلائل الإعجاز", "أسرار البلاغة", "مفتاح العلوم", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "أي علم من علوم البلاغة يهتم بالتشبيه والاستعارة؟",
        options: ["علم البيان", "علم المعاني", "علم البديع", "علم العروض"],
        correctAnswer: "علم البيان",
      ),
      QuestionModel(
        question: "ما هو الفرق بين التشبيه والاستعارة؟",
        options: ["التشبيه يذكر المشبه به والاستعارة لا تذكره", "لا فرق بينهما", "التشبيه أقوى من الاستعارة", "الاستعارة أوضح من التشبيه"],
        correctAnswer: "التشبيه يذكر المشبه به والاستعارة لا تذكره",
      ),
      QuestionModel(
        question: "من هو صاحب كتاب 'العمدة في محاسن الشعر'؟",
        options: ["ابن رشيق القيرواني", "قدامة بن جعفر", "ابن طباطبا", "الآمدي"],
        correctAnswer: "ابن رشيق القيرواني",
      ),
      QuestionModel(
        question: "ما هو أول كتاب في النقد الأدبي؟",
        options: ["نقد الشعر لقدامة بن جعفر", "طبقات الشعراء لابن سلام", "العمدة لابن رشيق", "الموازنة للآمدي"],
        correctAnswer: "طبقات الشعراء لابن سلام",
      ),
      QuestionModel(
        question: "من هو صاحب كتاب 'الموازنة بين الطائيين'؟",
        options: ["الآمدي", "ابن رشيق", "قدامة بن جعفر", "الجرجاني"],
        correctAnswer: "الآمدي",
      ),
      QuestionModel(
        question: "أي محسن بديعي يعتمد على تكرار الحروف؟",
        options: ["الجناس", "السجع", "الطباق", "المقابلة"],
        correctAnswer: "الجناس",
      ),
    ],
  ),

  // المستوى 10: أدب عالمي وترجمات (الأصعب)
  LevelModel(
    level: 10,
    title: "الأدب العالمي",
    category: QuizCategory.literature,
    backgroundGradient: const [
      Color(0xFF4A148C),
      Color(0xFF6A1B9A),
      Color(0xFF8E24AA),
      Color(0xFFAB47BC),
    ],
    questions: [
      QuestionModel(
        question: "من هو مؤلف 'الإلياذة' و'الأوديسة'؟",
        options: ["هوميروس", "سوفوكليس", "أرسطو", "أفلاطون"],
        correctAnswer: "هوميروس",
      ),
      QuestionModel(
        question: "من هو مؤلف 'الكوميديا الإلهية'؟",
        options: ["دانتي", "شكسبير", "سرفانتس", "جوته"],
        correctAnswer: "دانتي",
      ),
      QuestionModel(
        question: "من هو مؤلف 'دون كيخوته'؟",
        options: ["سرفانتس", "شكسبير", "دانتي", "جوته"],
        correctAnswer: "سرفانتس",
      ),
      QuestionModel(
        question: "من هو مؤلف 'هاملت'؟",
        options: ["شكسبير", "دانتي", "سرفانتس", "جوته"],
        correctAnswer: "شكسبير",
      ),
      QuestionModel(
        question: "من هو مؤلف 'فاوست'؟",
        options: ["جوته", "شيلر", "هاينه", "نيتشه"],
        correctAnswer: "جوته",
      ),
      QuestionModel(
        question: "من هو مؤلف 'الحرب والسلام'؟",
        options: ["تولستوي", "دوستويفسكي", "تشيخوف", "غوغول"],
        correctAnswer: "تولستوي",
      ),
      QuestionModel(
        question: "من هو مؤلف 'الجريمة والعقاب'؟",
        options: ["دوستويفسكي", "تولستوي", "تشيخوف", "غوغول"],
        correctAnswer: "دوستويفسكي",
      ),
      QuestionModel(
        question: "من هو أول من ترجم 'الإلياذة' إلى العربية؟",
        options: ["سليمان البستاني", "أحمد زكي باشا", "طه حسين", "أحمد شوقي"],
        correctAnswer: "سليمان البستاني",
      ),
      QuestionModel(
        question: "أي كاتب عربي ترجم أعمال شكسبير؟",
        options: ["خليل مطران", "أحمد شوقي", "طه حسين", "جميع ما سبق"],
        correctAnswer: "جميع ما سبق",
      ),
      QuestionModel(
        question: "من هو مؤلف 'ألف ليلة وليلة'؟",
        options: ["مؤلف مجهول", "الجاحظ", "ابن المقفع", "الهمذاني"],
        correctAnswer: "مؤلف مجهول",
      ),
    ],
  ),
];

/// --- توحيد جميع المستويات في قائمة شاملة ---
final List<LevelModel> allLevels = [
  ...islamicLevels,
  ...mathLevels,
  ...historicalLevels,
  ...videoGamesLevels,
  ...sportsLevels,
  ...literatureLevels,
];

/// هذه القائمة تبقى للتوافق مع الكود القديم
final List<LevelModel> defaultLevels = islamicLevels;
