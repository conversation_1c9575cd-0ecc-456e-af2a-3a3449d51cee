import 'dart:async';
import 'dart:math';
import 'dart:convert'; // إضافة استيراد للتعامل مع JSON
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_localizations/flutter_localizations.dart'; // Import localization delegates
import 'models.dart';
import 'dart:math' as math;

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  runApp(const MusabkatApp());
}

class MusabkatApp extends StatelessWidget {
  const MusabkatApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'المسابقات التعليمية',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        textTheme: GoogleFonts.cairoTextTheme(
          Theme.of(context).textTheme,
        ),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF03396C),
          primary: const Color(0xFF03396C),
          secondary: const Color(0xFFFCA311),
        ),
        useMaterial3: true,
      ),
      // ضبط التطبيق لاستخدام اللغة العربية واتجاه من اليمين لليسار
      locale: const Locale('ar', 'SA'),
      localizationsDelegates: const [ // Add localization delegates
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', 'SA'), // Keep Arabic locale
        // Locale('en', ''), // Optionally add English or other locales if needed
      ],
      home: const HomePage(),
    );
  }
}

// --- Category Data ---
// enum QuizCategory { islamic, math } // Moved to models.dart

class CategoryInfo {
  final QuizCategory category; // Use QuizCategory from models.dart
  final String title;
  final IconData icon;
  final List<Color> gradient;

  CategoryInfo({
    required this.category,
    required this.title,
    required this.icon,
    required this.gradient,
  });
}

// --- HomePage ---
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver {
  // Separate lists for levels
  List<LevelModel> islamicLevels = [];
  List<LevelModel> mathLevels = [];
  List<LevelModel> historicalLevels = []; // Added historical levels list
  List<LevelModel> videoGamesLevels = []; // Added video games levels list
  List<LevelModel> sportsLevels = []; // Added sports levels list
  List<LevelModel> literatureLevels = []; // Added literature levels list
  List<LevelModel> animalsLevels = []; // Added animals levels list
  List<LevelModel> plantsLevels = [];
  List<LevelModel> carsLevels = []; // Added plants levels list

  late AudioService audioService;
  bool isLoading = true;

  // Define categories
  final List<CategoryInfo> categories = [
    CategoryInfo(
      category: QuizCategory.islamic,
      title: "المسابقات الإسلامية",
      icon: Icons.mosque,
      gradient: [
        const Color(0xFF0F172A), // Slate 900 - عمق روحاني
        const Color(0xFF1E293B), // Slate 800 - هدوء مقدس
        const Color(0xFF334155), // Slate 700 - سكينة
        const Color(0xFF475569), // Slate 600 - تأمل
        const Color(0xFF64748B), // Slate 500 - حكمة
        const Color(0xFF94A3B8), // Slate 400 - نور
        const Color(0xFFCBD5E1), // Slate 300 - صفاء
        const Color(0xFFF1F5F9), // Slate 50 - طهارة
      ],
    ),
    CategoryInfo(
      category: QuizCategory.math,
      title: "المسابقات الحسابية",
      icon: Icons.calculate,
      gradient: [
        const Color(0xFF4C1D95), // Violet 900 - عمق رياضي
        const Color(0xFF5B21B6), // Violet 800 - تفكير منطقي
        const Color(0xFF6D28D9), // Violet 700 - إبداع حسابي
        const Color(0xFF7C3AED), // Violet 600 - ذكاء
        const Color(0xFF8B5CF6), // Violet 500 - إلهام
        const Color(0xFFA78BFA), // Violet 400 - ابتكار
        const Color(0xFFC4B5FD), // Violet 300 - تميز
        const Color(0xFFEDE9FE), // Violet 100 - إشراق
      ],
    ),
    CategoryInfo( // Add Historical Category
      category: QuizCategory.historical,
      title: "المسابقات التاريخية",
      icon: Icons.history_edu, // Example icon
      gradient: [ // Example gradient for Historical
        const Color(0xFF7F1D1D), // Red 900 - عراقة التاريخ
        const Color(0xFF991B1B), // Red 800 - قوة الماضي
        const Color(0xFFB91C1C), // Red 700 - شموخ الحضارات
        const Color(0xFFDC2626), // Red 600 - عظمة التراث
        const Color(0xFFEF4444), // Red 500 - حيوية التاريخ
        const Color(0xFFF87171), // Red 400 - إشراق الذكريات
        const Color(0xFFFCA5A5), // Red 300 - دفء الماضي
        const Color(0xFFFEE2E2), // Red 100 - نور المعرفة
      ],
    ),
    // Add a new category for video games
    CategoryInfo(
      category: QuizCategory.videoGames, // Add this to the QuizCategory enum in models.dart
      title: "مسابقات ألعاب الفيديو",
      icon: Icons.videogame_asset,
      gradient: [
        const Color(0xFF6A0DAD), // Purple gradient for gaming category
        const Color(0xFF9370DB),
        const Color(0xFFB19CD9),
        const Color(0xFFD8BFD8),
      ],
    ),
    // Add sports category
    CategoryInfo(
      category: QuizCategory.sports,
      title: "مسابقة الرياضة",
      icon: Icons.sports_soccer, // كرة قدم كأيقونة
      gradient: [
        const Color(0xFF1B5E20), // أخضر داكن مثل ملعب كرة القدم
        const Color(0xFF2E7D32),
        const Color(0xFF43A047),
        const Color(0xFF66BB6A),
      ],
    ),
    // Add literature category
    CategoryInfo(
      category: QuizCategory.literature,
      title: "مسابقة الأدب والشعر",
      icon: Icons.menu_book, // كتاب كأيقونة
      gradient: [
        const Color(0xFF4A148C), // بنفسجي داكن للأدب
        const Color(0xFF6A1B9A),
        const Color(0xFF8E24AA),
        const Color(0xFFAB47BC),
      ],
    ),
    // Add a new category for animals
    CategoryInfo(
      category: QuizCategory.animals,
      title: "مسابقات عالم الحيوانات",
      icon: Icons.cruelty_free, // أيقونة حيوانات أفضل من pets
      gradient: [
        const Color(0xFF7C2D12), // Brown 900 - عمق الأرض البرية
        const Color(0xFF9A3412), // Brown 800 - قوة الحيوانات
        const Color(0xFFEA580C), // Orange 600 - حيوية الحياة البرية
        const Color(0xFFF97316), // Orange 500 - طاقة الحيوانات
        const Color(0xFFFB923C), // Orange 400 - دفء الطبيعة
        const Color(0xFFFDBA74), // Orange 300 - إشراق الحياة
        const Color(0xFFFED7AA), // Orange 200 - نعومة الفراء
        const Color(0xFFFEF3C7), // Amber 100 - ذهبية الطبيعة
      ],
    ),

    // Add a new category for plants
    CategoryInfo(
      category: QuizCategory.plants,
      title: "مسابقات عالم النباتات",
      icon: Icons.eco, // أيقونة أوراق النبات البيئية
      gradient: [
        const Color(0xFF1B5E20), // Dark emerald green
        const Color(0xFF2E7D32), // Deep forest green
        const Color(0xFF43A047), // Rich green
        const Color(0xFF66BB6A), // Fresh green
        const Color(0xFF81C784), // Light fresh green
        const Color(0xFF4CAF50), // Vibrant green
        const Color(0xFF8BC34A), // Lime green
        const Color(0xFF9CCC65), // Light lime
      ],
    ),
    // Add a new category for cars
    CategoryInfo(
      category: QuizCategory.cars,
      title: "مسابقات عالم السيارات",
      icon: Icons.directions_car, // أيقونة السيارات
      gradient: [
        const Color(0xFF1E1B4B), // Indigo 900 - عمق الليل
        const Color(0xFF312E81), // Indigo 800 - قوة المحرك
        const Color(0xFF3730A3), // Indigo 700 - سرعة الطريق
        const Color(0xFF4338CA), // Indigo 600 - إثارة القيادة
        const Color(0xFF4F46E5), // Indigo 500 - حيوية السيارات
        const Color(0xFF6366F1), // Indigo 400 - تقنية متطورة
        const Color(0xFF818CF8), // Indigo 300 - أناقة التصميم
        const Color(0xFFC7D2FE), // Indigo 200 - لمعان المعدن
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    audioService = AudioService();
    WidgetsBinding.instance.addObserver(this);
    initApp();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    audioService.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused || state == AppLifecycleState.detached) {
      // حفظ جميع البيانات عند إيقاف التطبيق مؤقتاً أو إغلاقه
      _saveAllData();
    } else if (state == AppLifecycleState.resumed) {
      // إعادة تحميل البيانات عند العودة للتطبيق
      _reloadDataOnResume();
    }
  }

  // حفظ جميع البيانات
  Future<void> _saveAllData() async {
    try {
      debugPrint('💾 حفظ جميع البيانات عند تغيير حالة التطبيق...');

      await Future.wait([
        _saveLevelsData(QuizCategory.islamic),
        _saveLevelsData(QuizCategory.math),
        _saveLevelsData(QuizCategory.historical),
        _saveLevelsData(QuizCategory.videoGames),
        _saveLevelsData(QuizCategory.sports),
        _saveLevelsData(QuizCategory.literature),
        _saveLevelsData(QuizCategory.animals),
        _saveLevelsData(QuizCategory.plants),
        _saveLevelsData(QuizCategory.cars),
      ]);

      debugPrint('✅ تم حفظ جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ جميع البيانات: $e');
    }
  }

  // إعادة تحميل البيانات عند العودة للتطبيق
  Future<void> _reloadDataOnResume() async {
    try {
      debugPrint('🔄 إعادة تحميل البيانات عند العودة للتطبيق...');
      await _loadSavedData();
      if (mounted) {
        setState(() {
          // تحديث الواجهة
        });
      }
      debugPrint('✅ تم إعادة تحميل البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل البيانات: $e');
    }
  }

  Future<void> initApp() async {
    // تحميل البيانات المحفوظة من التخزين المحلي
    await _loadSavedData(); // Load all categories data
    _populateMathLevels(); // Create default math levels if not loaded
    _populateHistoricalLevels(); // Create default historical levels if not loaded
    _populateAnimalsLevels(); // Create default animals levels if not loaded
    _populatePlantsLevels(); // Create default plants levels if not loaded
    _populateCarsLevels(); // Create default cars levels if not loaded

    await audioService.initialize();

    setState(() {
      isLoading = false;
    });
  }

  // Load data for all categories
  Future<void> _loadSavedData() async {
    try {
      // Load Islamic Levels
      islamicLevels = List.from(defaultLevels); // Assuming defaultLevels are Islamic
      final prefs = await SharedPreferences.getInstance();
      final savedIslamicData = prefs.getString('islamic_levels_data');

      if (savedIslamicData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedIslamicData);
        for (var i = 0; i < savedLevels.length && i < islamicLevels.length; i++) {
          final savedLevel = savedLevels[i];
          islamicLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          islamicLevels[i].stars = savedLevel['stars'] ?? 0;
          islamicLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      }

      // Load Math Levels
      final savedMathData = prefs.getString('math_levels_data');
      _populateMathLevels(); // Ensure mathLevels list exists before loading
      if (savedMathData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedMathData);
        for (var i = 0; i < savedLevels.length && i < mathLevels.length; i++) {
          final savedLevel = savedLevels[i];
          mathLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          mathLevels[i].stars = savedLevel['stars'] ?? 0;
          mathLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      }

      // Load Historical Levels - استخدام القائمة الجاهزة من models.dart
      _populateHistoricalLevels(); // Ensure historicalLevels list exists before loading
      final savedHistoricalData = prefs.getString('historical_levels_data');
      if (savedHistoricalData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedHistoricalData);
        for (var i = 0; i < savedLevels.length && i < historicalLevels.length; i++) {
          final savedLevel = savedLevels[i];
          historicalLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          historicalLevels[i].stars = savedLevel['stars'] ?? 0;
          historicalLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      } else {
        // تأكد من أن المستوى الأول مفتوح افتراضيًا
        if (historicalLevels.isNotEmpty) {
          historicalLevels[0].isUnlocked = true;
        }
      }
      
      // Load Video Games Levels
      final savedVideoGamesData = prefs.getString('video_games_levels_data');
      _populateVideoGamesLevels(); // Ensure videoGamesLevels list exists before loading
      if (savedVideoGamesData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedVideoGamesData);
        for (var i = 0; i < savedLevels.length && i < videoGamesLevels.length; i++) {
          final savedLevel = savedLevels[i];
          videoGamesLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          videoGamesLevels[i].stars = savedLevel['stars'] ?? 0;
          videoGamesLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      }

      // Load Sports Levels
      final savedSportsData = prefs.getString('sports_levels_data');
      _populateSportsLevels(); // Ensure sportsLevels list exists before loading
      if (savedSportsData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedSportsData);
        for (var i = 0; i < savedLevels.length && i < sportsLevels.length; i++) {
          final savedLevel = savedLevels[i];
          sportsLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          sportsLevels[i].stars = savedLevel['stars'] ?? 0;
          sportsLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      }

      // Load Literature Levels
      final savedLiteratureData = prefs.getString('literature_levels_data');
      _populateLiteratureLevels(); // Ensure literatureLevels list exists before loading
      if (savedLiteratureData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedLiteratureData);
        for (var i = 0; i < savedLevels.length && i < literatureLevels.length; i++) {
          final savedLevel = savedLevels[i];
          literatureLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          literatureLevels[i].stars = savedLevel['stars'] ?? 0;
          literatureLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      }

      // Load Animals Levels
      final savedAnimalsData = prefs.getString('animals_levels_data');
      _populateAnimalsLevels(); // Ensure animalsLevels list exists before loading
      if (savedAnimalsData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedAnimalsData);
        for (var i = 0; i < savedLevels.length && i < animalsLevels.length; i++) {
          final savedLevel = savedLevels[i];
          animalsLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          animalsLevels[i].stars = savedLevel['stars'] ?? 0;
          animalsLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      }

      // Load Plants Levels
      final savedPlantsData = prefs.getString('plants_levels_data');
      _populatePlantsLevels(); // Ensure plantsLevels list exists before loading
      if (savedPlantsData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedPlantsData);
        for (var i = 0; i < savedLevels.length && i < plantsLevels.length; i++) {
          final savedLevel = savedLevels[i];
          plantsLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          plantsLevels[i].stars = savedLevel['stars'] ?? 0;
          plantsLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      }

      // Load Cars Levels
      final savedCarsData = prefs.getString('cars_levels_data');
      _populateCarsLevels(); // Ensure carsLevels list exists before loading
      if (savedCarsData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedCarsData);
        for (var i = 0; i < savedLevels.length && i < carsLevels.length; i++) {
          final savedLevel = savedLevels[i];
          carsLevels[i].isUnlocked = savedLevel['isUnlocked'] ?? (i == 0);
          carsLevels[i].stars = savedLevel['stars'] ?? 0;
          carsLevels[i].highScore = savedLevel['highScore'] ?? 0;
        }
      }

    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');
      // Fallback to defaults
      islamicLevels = List.from(defaultLevels);
      _populateMathLevels(); // Ensure defaults are populated on error
      // تأكد من أن المستوى التاريخي الأول مفتوح على الأقل
      if (historicalLevels.isEmpty) {
        historicalLevels = List.from(historicalLevels); // استخدام القائمة المعرفة في models.dart
        if (historicalLevels.isNotEmpty) {
          historicalLevels[0].isUnlocked = true;
        }
      }
      _populateVideoGamesLevels(); // Ensure defaults are populated on error
      _populateSportsLevels(); // Ensure defaults are populated on error
      _populateLiteratureLevels(); // Ensure defaults are populated on error
    }

    setState(() {
      isLoading = false;
    });

    debugPrint('✅ تم تحميل البيانات بنجاح');
  }

  // وظيفة إضافية لضمان حفظ التقدم فوراً عند إكمال المستوى
  Future<void> _forceProgressSave(QuizCategory category, int levelIndex, int stars, int score) async {
    debugPrint('🚀 حفظ فوري للتقدم: الفئة=$category, المستوى=$levelIndex, النجوم=$stars, النقاط=$score');

    try {
      final prefs = await SharedPreferences.getInstance();
      String key = '';
      List<LevelModel> targetLevels = [];

      switch (category) {
        case QuizCategory.islamic:
          key = 'islamic_levels_data';
          targetLevels = islamicLevels;
          break;
        case QuizCategory.math:
          key = 'math_levels_data';
          targetLevels = mathLevels;
          break;
        case QuizCategory.historical:
          key = 'historical_levels_data';
          targetLevels = historicalLevels;
          break;
        case QuizCategory.videoGames:
          key = 'video_games_levels_data';
          targetLevels = videoGamesLevels;
          break;
        case QuizCategory.sports:
          key = 'sports_levels_data';
          targetLevels = sportsLevels;
          break;
        case QuizCategory.literature:
          key = 'literature_levels_data';
          targetLevels = literatureLevels;
          break;
        case QuizCategory.animals:
          key = 'animals_levels_data';
          targetLevels = animalsLevels;
          break;
        case QuizCategory.plants:
          key = 'plants_levels_data';
          targetLevels = plantsLevels;
          break;
        case QuizCategory.cars:
          key = 'cars_levels_data';
          targetLevels = carsLevels;
          break;
      }

      if (levelIndex >= 0 && levelIndex < targetLevels.length) {
        // تحديث البيانات في الذاكرة
        if (stars > targetLevels[levelIndex].stars) {
          targetLevels[levelIndex].stars = stars;
        }
        if (score > targetLevels[levelIndex].highScore) {
          targetLevels[levelIndex].highScore = score;
        }

        // فتح المستوى التالي
        if (stars > 0 && levelIndex + 1 < targetLevels.length) {
          targetLevels[levelIndex + 1].isUnlocked = true;
        }

        // حفظ فوري
        final List<Map<String, dynamic>> levelsData = targetLevels.map((level) => {
          'level': level.level,
          'isUnlocked': level.isUnlocked,
          'stars': level.stars,
          'highScore': level.highScore,
        }).toList();

        await prefs.setString(key, jsonEncode(levelsData));
        await prefs.setInt('${key}_timestamp', DateTime.now().millisecondsSinceEpoch);

        debugPrint('💾 تم الحفظ الفوري بنجاح للفئة: $category');
      }
    } catch (e) {
      debugPrint('❌ خطأ في الحفظ الفوري: $e');
    }
  }

  // Save data for a specific category - محسن مع إعادة المحاولة
  Future<void> _saveLevelsData(QuizCategory category) async {
    int maxRetries = 3;
    int currentRetry = 0;

    while (currentRetry < maxRetries) {
      try {
        final prefs = await SharedPreferences.getInstance();
        String key = '';
        List<LevelModel> levelsToSave = [];

        switch (category) {
          case QuizCategory.islamic:
            key = 'islamic_levels_data';
            levelsToSave = islamicLevels;
            break;
          case QuizCategory.math:
            key = 'math_levels_data';
            levelsToSave = mathLevels;
            break;
          case QuizCategory.historical:
            key = 'historical_levels_data';
            levelsToSave = historicalLevels;
            break;
          case QuizCategory.videoGames:
            key = 'video_games_levels_data';
            levelsToSave = videoGamesLevels;
            break;
          case QuizCategory.sports:
            key = 'sports_levels_data';
            levelsToSave = sportsLevels;
            break;
          case QuizCategory.literature:
            key = 'literature_levels_data';
            levelsToSave = literatureLevels;
            break;
          case QuizCategory.animals:
            key = 'animals_levels_data';
            levelsToSave = animalsLevels;
            break;
          case QuizCategory.plants:
            key = 'plants_levels_data';
            levelsToSave = plantsLevels;
            break;
          case QuizCategory.cars:
            key = 'cars_levels_data';
            levelsToSave = carsLevels;
            break;
        }

        if (key.isNotEmpty && levelsToSave.isNotEmpty) {
          // إنشاء نسخة من البيانات للحفظ
          final List<Map<String, dynamic>> levelsData = levelsToSave.map((level) => {
            'level': level.level,
            'isUnlocked': level.isUnlocked,
            'stars': level.stars,
            'highScore': level.highScore,
          }).toList();

          // حفظ البيانات
          final jsonData = jsonEncode(levelsData);
          await prefs.setString(key, jsonData);

          // التحقق من الحفظ
          final savedData = prefs.getString(key);
          if (savedData == jsonData) {
            debugPrint('✅ تم حفظ بيانات مستويات ${category.name} بنجاح (المحاولة ${currentRetry + 1})');

            // إضافة طابع زمني للحفظ
            await prefs.setInt('${key}_timestamp', DateTime.now().millisecondsSinceEpoch);

            return; // نجح الحفظ، الخروج من الحلقة
          } else {
            throw Exception('فشل في التحقق من الحفظ');
          }
        } else {
          debugPrint('❌ خطأ: مفتاح أو قائمة مستويات غير صالحة للحفظ لـ ${category.name}');
          return;
        }

      } catch (e) {
        currentRetry++;
        debugPrint('❌ خطأ في حفظ بيانات ${category.name} (المحاولة $currentRetry): $e');

        if (currentRetry < maxRetries) {
          debugPrint('🔄 إعادة المحاولة بعد ${currentRetry * 100} مللي ثانية...');
          await Future.delayed(Duration(milliseconds: currentRetry * 100));
        } else {
          debugPrint('💥 فشل في حفظ البيانات بعد $maxRetries محاولات');
        }
      }
    }
  }

  // حساب إجمالي النجوم في فئة معينة
  int _calculateTotalStars(List<LevelModel> levels) {
    int totalStars = 0;
    for (var level in levels) {
      totalStars += level.stars;
    }
    return totalStars;
  }

  // عرض رسالة فتح المستوى الخاص مع تأثيرات جمالية
  void _showSpecialUnlockMessage(int level, int requiredStars) {
    debugPrint('🎉 تم فتح المستوى الخاص $level بـ $requiredStars نجمة!');

    // إضافة رسالة مؤقتة في السياق المناسب
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _showAnimatedUnlockDialog(level, requiredStars);
      }
    });
  }

  // عرض حوار فتح المستوى المتحرك
  void _showAnimatedUnlockDialog(int level, int requiredStars) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 800),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, animationValue, child) {
            return Transform.scale(
              scale: 0.3 + (animationValue * 0.7),
              child: Opacity(
                opacity: animationValue,
                child: AlertDialog(
                  backgroundColor: Colors.transparent,
                  content: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.amber,
                          Colors.orange,
                          Colors.deepOrange,
                          Colors.red.shade400,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.withOpacity(0.6),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                          spreadRadius: 5,
                        ),
                      ],
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // أيقونة متحركة
                        TweenAnimationBuilder<double>(
                          duration: const Duration(seconds: 2),
                          tween: Tween(begin: 0.0, end: 1.0),
                          builder: (context, value, child) {
                            return Transform.rotate(
                              angle: value * math.pi * 2,
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: RadialGradient(
                                    colors: [
                                      Colors.white,
                                      Colors.yellow.shade300,
                                      Colors.amber,
                                    ],
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.white.withOpacity(0.8),
                                      blurRadius: 15,
                                      spreadRadius: 3,
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.lock_open,
                                  size: 40,
                                  color: Colors.orange.shade800,
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 20),
                        // النص الرئيسي
                        Text(
                          '🎉 مبروك! 🎉',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.5),
                                blurRadius: 3,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 10),
                        Text(
                          'تم فتح المستوى الخاص',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 15),
                        // رقم المستوى
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.4),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            'المستوى $level',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w800,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 15),
                        // النجوم المطلوبة
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'بـ $requiredStars',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 5),
                            Icon(
                              Icons.star,
                              color: Colors.yellow.shade300,
                              size: 20,
                            ),
                            const SizedBox(width: 5),
                            const Text(
                              'نجمة',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        // زر الإغلاق
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.orange.shade800,
                            padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            elevation: 5,
                          ),
                          child: const Text(
                            'رائع!',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );

    // إغلاق تلقائي بعد 5 ثوان
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    });
  }

  // فتح المستويات المتوسطة بناءً على النجوم
  void _unlockIntermediateLevels(List<LevelModel> targetLevels, int totalStars) {
    bool changed = false;

    // فتح المستويات بناءً على النجوم المجمعة
    final unlockRequirements = {
      2: 3,   // المستوى 3 يحتاج 3 نجوم
      3: 6,   // المستوى 4 يحتاج 6 نجوم
      5: 15,  // المستوى 6 يحتاج 15 نجمة
      6: 18,  // المستوى 7 يحتاج 18 نجمة
      7: 21,  // المستوى 8 يحتاج 21 نجمة
      8: 24,  // المستوى 9 يحتاج 24 نجمة
      10: 30, // المستوى 11 يحتاج 30 نجمة
      11: 33, // المستوى 12 يحتاج 33 نجمة
      12: 36, // المستوى 13 يحتاج 36 نجمة
      13: 39, // المستوى 14 يحتاج 39 نجمة
      14: 42, // المستوى 15 يحتاج 42 نجمة
      15: 45, // المستوى 16 يحتاج 45 نجمة
      16: 48, // المستوى 17 يحتاج 48 نجمة
      17: 51, // المستوى 18 يحتاج 51 نجمة
      18: 54, // المستوى 19 يحتاج 54 نجمة
      19: 57, // المستوى 20 يحتاج 57 نجمة
    };

    for (var entry in unlockRequirements.entries) {
      int levelIndex = entry.key;
      int requiredStars = entry.value;

      if (totalStars >= requiredStars &&
          levelIndex < targetLevels.length &&
          !targetLevels[levelIndex].isUnlocked) {
        debugPrint('🔓 فتح المستوى ${levelIndex + 1} بـ $requiredStars نجمة');
        targetLevels[levelIndex].isUnlocked = true;
        changed = true;
      }
    }
  }

  // Update progress for a specific category - محسن مع حفظ فوري
  void _updateLevelProgress(QuizCategory category, int levelIndex, int stars, int score) async {
    debugPrint('🔄 تحديث التقدم: الفئة=$category, المستوى=$levelIndex, النجوم=$stars, النقاط=$score');

    List<LevelModel> targetLevels;

    switch (category) {
      case QuizCategory.islamic:
        targetLevels = islamicLevels;
        break;
      case QuizCategory.math:
        targetLevels = mathLevels;
        break;
      case QuizCategory.historical:
        targetLevels = historicalLevels;
        break;
      case QuizCategory.videoGames:
        targetLevels = videoGamesLevels;
        break;
      case QuizCategory.sports:
        targetLevels = sportsLevels;
        break;
      case QuizCategory.literature:
        targetLevels = literatureLevels;
        break;
      case QuizCategory.animals:
        targetLevels = animalsLevels;
        break;
      case QuizCategory.plants:
        targetLevels = plantsLevels;
        break;
      case QuizCategory.cars:
        targetLevels = carsLevels;
        break;
    }

    if (levelIndex < 0 || levelIndex >= targetLevels.length) {
      debugPrint('❌ فهرس المستوى غير صالح: $levelIndex');
      return;
    }

    bool changed = false;

    // تحديث النجوم إذا كانت أفضل
    if (stars > targetLevels[levelIndex].stars) {
      debugPrint('⭐ تحديث النجوم من ${targetLevels[levelIndex].stars} إلى $stars');
      targetLevels[levelIndex].stars = stars;
      changed = true;
    }

    // تحديث أعلى نقاط إذا كانت أفضل
    if (score > targetLevels[levelIndex].highScore) {
      debugPrint('🏆 تحديث أعلى نقاط من ${targetLevels[levelIndex].highScore} إلى $score');
      targetLevels[levelIndex].highScore = score;
      changed = true;
    }

    // نظام فتح المستويات المحسن بناءً على النجوم المجمعة
    if (stars > 0) {
      // فتح المستوى التالي مباشرة إذا كان متاح
      if (levelIndex + 1 < targetLevels.length && !targetLevels[levelIndex + 1].isUnlocked) {
        debugPrint('🔓 فتح المستوى التالي: ${levelIndex + 2}');
        targetLevels[levelIndex + 1].isUnlocked = true;
        changed = true;
      }

      // فتح المستويات الخاصة بناءً على النجوم المجمعة
      final totalStars = _calculateTotalStars(targetLevels);
      debugPrint('⭐ إجمالي النجوم: $totalStars');

      // فتح المستوى 5 إذا حصل على 10 نجوم - مع فحص دقيق
      if (targetLevels.length >= 5 && !targetLevels[4].isUnlocked) {
        // حساب النجوم من المستويات 1-4 فقط
        int starsFromFirstFourLevels = 0;
        for (int i = 0; i < 4 && i < targetLevels.length; i++) {
          starsFromFirstFourLevels += targetLevels[i].stars;
        }

        debugPrint('⭐ النجوم من المستويات 1-4: $starsFromFirstFourLevels');

        if (starsFromFirstFourLevels >= 10) {
          debugPrint('🌟 فتح المستوى الخاص 5 بـ $starsFromFirstFourLevels نجمة!');
          targetLevels[4].isUnlocked = true;
          changed = true;
          _showSpecialUnlockMessage(5, 10);
        }
      }

      // فتح المستوى 10 إذا حصل على 27 نجمة
      if (totalStars >= 27 && targetLevels.length >= 10 && !targetLevels[9].isUnlocked) {
        debugPrint('💫 فتح المستوى الخاص 10 بـ 27 نجمة!');
        targetLevels[9].isUnlocked = true;
        changed = true;
        _showSpecialUnlockMessage(10, 27);
      }

      // فتح المستويات المتوسطة بناءً على النجوم
      _unlockIntermediateLevels(targetLevels, totalStars);
    }

    if (changed) {
      debugPrint('💾 حفظ التغييرات للفئة: $category');

      // تحديث الواجهة فوراً
      setState(() {
        // State update happens implicitly by modifying the list item
      });

      // حفظ البيانات المحدثة فوراً
      await _saveLevelsData(category);

      // التحقق من الحفظ
      await _verifySavedData(category, levelIndex, stars, score);

      debugPrint('✅ تم تحديث وحفظ التقدم بنجاح');
    } else {
      debugPrint('ℹ️ لا توجد تغييرات للحفظ');
    }
  }

  // التحقق من صحة البيانات المحفوظة
  Future<void> _verifySavedData(QuizCategory category, int levelIndex, int expectedStars, int expectedScore) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String key = '';

      switch (category) {
        case QuizCategory.islamic:
          key = 'islamic_levels_data';
          break;
        case QuizCategory.math:
          key = 'math_levels_data';
          break;
        case QuizCategory.historical:
          key = 'historical_levels_data';
          break;
        case QuizCategory.videoGames:
          key = 'video_games_levels_data';
          break;
        case QuizCategory.sports:
          key = 'sports_levels_data';
          break;
        case QuizCategory.literature:
          key = 'literature_levels_data';
          break;
        case QuizCategory.animals:
          key = 'animals_levels_data';
          break;
        case QuizCategory.plants:
          key = 'plants_levels_data';
          break;
        case QuizCategory.cars:
          key = 'cars_levels_data';
          break;
      }

      final savedData = prefs.getString(key);
      if (savedData != null) {
        final List<dynamic> savedLevels = jsonDecode(savedData);
        if (levelIndex < savedLevels.length) {
          final savedLevel = savedLevels[levelIndex];
          debugPrint('🔍 التحقق من البيانات المحفوظة:');
          debugPrint('   النجوم المحفوظة: ${savedLevel['stars']} (متوقع: $expectedStars)');
          debugPrint('   النقاط المحفوظة: ${savedLevel['highScore']} (متوقع: $expectedScore)');

          if (savedLevel['stars'] != expectedStars || savedLevel['highScore'] != expectedScore) {
            debugPrint('⚠️ تضارب في البيانات المحفوظة! إعادة الحفظ...');
            await _saveLevelsData(category);
          }
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من البيانات المحفوظة: $e');
    }
  }

  // Placeholder for creating math levels
  void _populateMathLevels() {
     if (mathLevels.isEmpty) { // Only populate if empty
       mathLevels = List.generate(10, (index) {
         // Generate questions dynamically later
         return LevelModel(
           level: index + 1,
           title: "عمليات حسابية ${index + 1}",
           questions: _generateMathQuestions(index + 1),
           backgroundGradient: categories.firstWhere((c) => c.category == QuizCategory.math).gradient,
           category: QuizCategory.math, // Add the required category argument
           isUnlocked: index == 0, // First level unlocked
           stars: 0,
           highScore: 0,
         );
       });
     }
  }

  // Populate historical levels if empty
  void _populateHistoricalLevels() {
    if (historicalLevels.isEmpty) {
      // Importar los niveles históricos desde el archivo models.dart
      final modelHistoricalLevels = allLevels.where((level) => level.category == QuizCategory.historical).toList();
      
      if (modelHistoricalLevels.isNotEmpty) {
        historicalLevels = List.from(modelHistoricalLevels);
        
        // Asegurar que el primer nivel esté desbloqueado
        if (historicalLevels.isNotEmpty) {
          historicalLevels[0].isUnlocked = true;
        }
      }
    }
  }

  // Generates historical questions. Level 1 has real questions, others are placeholders.
  List<QuestionModel> _generateMathQuestions(int level) {
    List<QuestionModel> questions = [];
    Random random = Random();
    for (int i = 0; i < 10; i++) { // 10 questions per level
      int num1 = random.nextInt(level * 5) + level;
      int num2 = random.nextInt(level * 5) + 1; // Avoid division by zero
      int operationType = random.nextInt(min(level, 4)); // 0:+, 1:-, 2:*, 3:/

      String questionText;
      int correctAnswer;
      List<String> options = [];

      switch (operationType) {
        case 1: // Subtraction
          // Ensure positive result for simplicity initially
          if (num1 < num2) {
            int temp = num1;
            num1 = num2;
            num2 = temp;
          }
          questionText = "$num1 - $num2 = ?";
          correctAnswer = num1 - num2;
          break;
        case 2: // Multiplication
           num1 = random.nextInt(level * 2) + 2; // Smaller numbers for multiplication
           num2 = random.nextInt(8) + 2;
          questionText = "$num1 × $num2 = ?";
          correctAnswer = num1 * num2;
          break;
        case 3: // Division (ensure integer result)
           num2 = random.nextInt(level + 1) + 2; // Divisor
           correctAnswer = random.nextInt(level * 2) + 1; // Result
           num1 = correctAnswer * num2; // Calculate dividend
          questionText = "$num1 ÷ $num2 = ?";
          break;
        default: // Addition
          questionText = "$num1 + $num2 = ?";
          correctAnswer = num1 + num2;
          break;
      }

      options.add(correctAnswer.toString());
      // Generate 3 wrong options
      while (options.length < 4) {
        int wrongAnswer = correctAnswer + random.nextInt(level * 3 + 1) - (level);
        if (wrongAnswer != correctAnswer && !options.contains(wrongAnswer.toString()) && wrongAnswer >= 0) {
          options.add(wrongAnswer.toString());
        }
         // Add a simple check to prevent infinite loops in edge cases
         if (options.length == 1 && level == 1 && random.nextInt(100) > 95) {
            options.add((correctAnswer + 1).toString());
            options.add((correctAnswer + 2).toString());
            options.add((correctAnswer + 3).toString());
            options = options.toSet().toList(); // Ensure uniqueness
            while(options.length < 4) {
              options.add((correctAnswer + options.length + 5).toString()); // Fill remaining
            }
         }
      }
      options.shuffle();

      questions.add(QuestionModel(
        question: questionText,
        options: options,
        correctAnswer: correctAnswer.toString(),
      ));
    }
    return questions;
  }

  // Generates historical questions. Level 1 has real questions, others are placeholders.
  List<QuestionModel> _generateHistoricalQuestions(int level) {
    // استخدام الأسئلة من ملف models.dart
    List<QuestionModel> questions = [];
    
    // البحث عن المستوى المقابل في قائمة المستويات التاريخية بملف models.dart
    for (var historicalLevel in historicalLevels) {
      if (historicalLevel.level == level) {
        return historicalLevel.questions;
      }
    }
    
    // إذا لم يتم العثور على المستوى، إرجاع قائمة فارغة
    return questions;
  }

  // Populate video games levels if empty
  void _populateVideoGamesLevels() {
    if (videoGamesLevels.isEmpty) {
      // استخدام مستويات ألعاب الفيديو من models.dart
      final modelVideoGamesLevels = allLevels.where((level) => level.category == QuizCategory.videoGames).toList();

      if (modelVideoGamesLevels.isNotEmpty) {
        videoGamesLevels = List.from(modelVideoGamesLevels);

        // تأكد من أن المستوى الأول مفتوح
        if (videoGamesLevels.isNotEmpty) {
          videoGamesLevels[0].isUnlocked = true;
        }
      } else {
        // If the list is still empty (models data not loaded properly), generate placeholder levels
        videoGamesLevels = List.generate(10, (index) {
          return LevelModel(
            level: index + 1,
            title: "ألعاب الفيديو ${index + 1}",
            questions: _generateVideoGamesQuestions(index + 1),
            backgroundGradient: categories.firstWhere((c) => c.category == QuizCategory.videoGames).gradient,
            category: QuizCategory.videoGames,
            isUnlocked: index == 0,
            stars: 0,
            highScore: 0,
          );
        });
      }
    }
  }

  // Generates video games questions
  List<QuestionModel> _generateVideoGamesQuestions(int level) {
    // First try to get questions from models.dart
    List<LevelModel> modelLevels = allLevels.where((l) => l.category == QuizCategory.videoGames).toList();
    
    if (modelLevels.isNotEmpty && modelLevels.length >= level) {
      return modelLevels[level - 1].questions;
    }
    
    // Fallback - generate placeholder questions
    List<QuestionModel> questions = [];
    for (int i = 1; i <= 10; i++) {
      questions.add(QuestionModel(
        question: "سؤال ألعاب الفيديو للمستوى $level رقم $i؟",
        options: ["خيار 1", "خيار 2", "إجابة صحيحة $i", "خيار 4"],
        correctAnswer: "إجابة صحيحة $i",
      ));
    }
    return questions;
  }

  // Populate sports levels if empty
  void _populateSportsLevels() {
    if (sportsLevels.isEmpty) {
      // استخدام مستويات الرياضة من models.dart
      final modelSportsLevels = allLevels.where((level) => level.category == QuizCategory.sports).toList();

      if (modelSportsLevels.isNotEmpty) {
        sportsLevels = List.from(modelSportsLevels);

        // تأكد من أن المستوى الأول مفتوح
        if (sportsLevels.isNotEmpty) {
          sportsLevels[0].isUnlocked = true;
        }
      }
    }
  }

  // Populate literature levels if empty
  void _populateLiteratureLevels() {
    if (literatureLevels.isEmpty) {
      // استخدام مستويات الأدب من models.dart
      final modelLiteratureLevels = allLevels.where((level) => level.category == QuizCategory.literature).toList();

      if (modelLiteratureLevels.isNotEmpty) {
        literatureLevels = List.from(modelLiteratureLevels);

        // تأكد من أن المستوى الأول مفتوح
        if (literatureLevels.isNotEmpty) {
          literatureLevels[0].isUnlocked = true;
        }
      }
    }
  }

  // إنشاء مستويات الحيوانات الافتراضية
  void _populateAnimalsLevels() {
    if (animalsLevels.isEmpty) {
      animalsLevels = List.generate(15, (index) { // زيادة من 10 إلى 15 مستوى
        return LevelModel(
          level: index + 1,
          title: "عالم الحيوانات ${index + 1}",
          category: QuizCategory.animals,
          questions: _getAnimalsQuestions(index + 1),
          isUnlocked: index == 0, // فقط المستوى الأول مفتوح
          stars: 0,
          highScore: 0,
          backgroundGradient: [
            const Color(0xFF7C2D12), // بني داكن
            const Color(0xFF9A3412), // بني متوسط
            const Color(0xFFEA580C), // برتقالي داكن
            const Color(0xFFF97316), // برتقالي
          ],
        );
      });
    }
  }

  // إنشاء مستويات النباتات الافتراضية
  void _populatePlantsLevels() {
    if (plantsLevels.isEmpty) {
      plantsLevels = List.generate(10, (index) {
        return LevelModel(
          level: index + 1,
          title: "عالم النباتات ${index + 1}",
          category: QuizCategory.plants,
          questions: _getPlantsQuestions(index + 1),
          isUnlocked: index == 0, // المستوى الأول مفتوح
          backgroundGradient: [
            const Color(0xFF1B5E20), // أخضر زمردي داكن
            const Color(0xFF2E7D32), // أخضر غابات عميق
            const Color(0xFF43A047), // أخضر غني
            const Color(0xFF66BB6A), // أخضر منعش
            const Color(0xFF81C784), // أخضر فاتح منعش
            const Color(0xFF4CAF50), // أخضر نابض بالحياة
            const Color(0xFF8BC34A), // أخضر ليموني
            const Color(0xFF9CCC65), // أخضر ليموني فاتح
          ],
        );
      });
    }
  }

  // إنشاء مستويات السيارات الافتراضية
  void _populateCarsLevels() {
    if (carsLevels.isEmpty) {
      carsLevels = List.generate(10, (index) {
        return LevelModel(
          level: index + 1,
          title: "عالم السيارات ${index + 1}",
          category: QuizCategory.cars,
          questions: _getCarsQuestions(index + 1),
          isUnlocked: index == 0, // المستوى الأول مفتوح
          backgroundGradient: [
            const Color(0xFF1E1B4B), // Indigo 900
            const Color(0xFF312E81), // Indigo 800
            const Color(0xFF3730A3), // Indigo 700
            const Color(0xFF4338CA), // Indigo 600
          ],
        );
      });
    }
  }

  // الحصول على أسئلة الحيوانات حسب المستوى
  List<QuestionModel> _getAnimalsQuestions(int level) {
    switch (level) {
      case 1: // مستوى سهل - حيوانات أليفة ومعروفة
        return [
          QuestionModel(
            question: "ما هو الحيوان الذي يُعرف بأنه أفضل صديق للإنسان؟",
            options: ["🐕 الكلب", "🐱 القط", "🐰 الأرنب", "🐹 الهامستر"],
            correctAnswer: "🐕 الكلب",
          ),
          QuestionModel(
            question: "أي حيوان يُصدر صوت 'مواء'؟",
            options: ["🐕 الكلب", "🐱 القط", "🐄 البقرة", "🐑 الخروف"],
            correctAnswer: "🐱 القط",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُنتج الحليب؟",
            options: ["🐔 الدجاج", "🐄 البقرة", "🐷 الخنزير", "🐐 الماعز"],
            correctAnswer: "🐄 البقرة",
          ),
          QuestionModel(
            question: "أي حيوان له جذع طويل؟",
            options: ["🐘 الفيل", "🦒 الزرافة", "🐎 الحصان", "🦁 الأسد"],
            correctAnswer: "🐘 الفيل",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُبيض؟",
            options: ["🐄 البقرة", "🐕 الكلب", "🐔 الدجاج", "🐱 القط"],
            correctAnswer: "🐔 الدجاج",
          ),
          QuestionModel(
            question: "أي حيوان يُعرف بأنه ملك الغابة؟",
            options: ["🐅 النمر", "🦁 الأسد", "🐆 الفهد", "🐺 الذئب"],
            correctAnswer: "🦁 الأسد",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُحب أكل الجزر؟",
            options: ["🐰 الأرنب", "🐱 القط", "🐕 الكلب", "🐭 الفأر"],
            correctAnswer: "🐰 الأرنب",
          ),
          QuestionModel(
            question: "أي حيوان يُعيش في الماء ويُسبح؟",
            options: ["🐦 الطائر", "🐟 السمك", "🐕 الكلب", "🐱 القط"],
            correctAnswer: "🐟 السمك",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُصدر صوت 'نهيق'؟",
            options: ["🐎 الحصان", "🫏 الحمار", "🐄 البقرة", "🐑 الخروف"],
            correctAnswer: "🫏 الحمار",
          ),
          QuestionModel(
            question: "أي حيوان له رقبة طويلة جداً؟",
            options: ["🐘 الفيل", "🦒 الزرافة", "🐎 الحصان", "🐄 البقرة"],
            correctAnswer: "🦒 الزرافة",
          ),
        ];

      case 2: // مستوى سهل-متوسط - حيوانات المزرعة والبرية
        return [
          QuestionModel(
            question: "ما هو الحيوان الذي يُخزن الطعام في خديه؟",
            options: ["🐰 الأرنب", "🐹 الهامستر", "🐭 الفأر", "🐿️ السنجاب"],
            correctAnswer: "🐹 الهامستر",
          ),
          QuestionModel(
            question: "أي حيوان يُمكنه تغيير لون جلده؟",
            options: ["🦎 الحرباء", "🐸 الضفدع", "🦎 السحلية", "🐍 الثعبان"],
            correctAnswer: "🦎 الحرباء",
          ),
          QuestionModel(
            question: "ما هو أسرع حيوان بري في العالم؟",
            options: ["🦁 الأسد", "🐆 الفهد", "🐅 النمر", "🐺 الذئب"],
            correctAnswer: "🐆 الفهد",
          ),
          QuestionModel(
            question: "أي حيوان يُسمى صغيره 'مهر'؟",
            options: ["🐄 البقرة", "🐎 الحصان", "🐑 الخروف", "🐐 الماعز"],
            correctAnswer: "🐎 الحصان",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُنام واقفاً؟",
            options: ["🐄 البقرة", "🐎 الحصان", "🐑 الخروف", "🐐 الماعز"],
            correctAnswer: "🐎 الحصان",
          ),
          QuestionModel(
            question: "أي حيوان يُعرف بذكائه العالي؟",
            options: ["🐬 الدلفين", "🦈 القرش", "🐋 الحوت", "🐟 السمك"],
            correctAnswer: "🐬 الدلفين",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُصدر صوت 'زئير'؟",
            options: ["🐅 النمر", "🦁 الأسد", "🐆 الفهد", "🐺 الذئب"],
            correctAnswer: "🦁 الأسد",
          ),
          QuestionModel(
            question: "أي حيوان يُمكنه الطيران والسباحة؟",
            options: ["🦆 البطة", "🐔 الدجاج", "🐓 الديك", "🪿 الإوزة"],
            correctAnswer: "🦆 البطة",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُحمل صغاره في جيب؟",
            options: ["🦘 الكنغر", "🐨 الكوالا", "🐼 الباندا", "🐒 القرد"],
            correctAnswer: "🦘 الكنغر",
          ),
          QuestionModel(
            question: "أي حيوان يُعتبر أكبر حيوان بحري؟",
            options: ["🦈 القرش", "🐋 الحوت الأزرق", "🐬 الدلفين", "🐙 الأخطبوط"],
            correctAnswer: "🐋 الحوت الأزرق",
          ),
        ];

      default:
        return _getAdvancedAnimalsQuestions(level);
    }
  }

  // الحصول على أسئلة الحيوانات المتقدمة للمستويات 3-10
  List<QuestionModel> _getAdvancedAnimalsQuestions(int level) {
    switch (level) {
      case 3: // حيوانات بحرية ومائية
        return [
          QuestionModel(
            question: "ما هو أكبر حيوان في العالم؟",
            options: ["🐘 الفيل الأفريقي", "🐋 الحوت الأزرق", "🦈 القرش الأبيض", "🦑 الحبار العملاق"],
            correctAnswer: "🐋 الحوت الأزرق",
          ),
          QuestionModel(
            question: "أي حيوان بحري له ثمانية أذرع؟",
            options: ["🦑 الحبار", "🐙 الأخطبوط", "🪼 قنديل البحر", "⭐ نجم البحر"],
            correctAnswer: "🐙 الأخطبوط",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُغير جنسه؟",
            options: ["🐠 السمك المهرج", "🐸 الضفدع", "🦎 الحرباء", "🐍 الثعبان"],
            correctAnswer: "🐠 السمك المهرج",
          ),
          QuestionModel(
            question: "أي حيوان يُستخدم في البحث عن الكمأة؟",
            options: ["🐕 الكلب", "🐷 الخنزير", "🐱 القط", "🐰 الأرنب"],
            correctAnswer: "🐷 الخنزير",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُمكنه البقاء تحت الماء لأطول فترة؟",
            options: ["🐬 الدلفين", "🐋 الحوت الأزرق", "🐋 حوت العنبر", "🦭 الفقمة"],
            correctAnswer: "🐋 حوت العنبر",
          ),
          QuestionModel(
            question: "أي حيوان له أقوى عضة في العالم؟",
            options: ["🦁 الأسد", "🐊 التمساح", "🦈 القرش", "🐺 الضبع"],
            correctAnswer: "🐊 التمساح",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُولد أعمى؟",
            options: ["🐱 القط", "🐕 الكلب", "🐰 الأرنب", "🐭 الفأر"],
            correctAnswer: "🐱 القط",
          ),
          QuestionModel(
            question: "أي حيوان يُمكنه الرؤية بالألوان الفوق بنفسجية؟",
            options: ["🐝 النحل", "🦋 الفراشة", "🐦 الطائر الطنان", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي له أكبر عيون في العالم؟",
            options: ["🦑 الحبار العملاق", "🐋 الحوت", "🐘 الفيل", "🦢 النعامة"],
            correctAnswer: "🦑 الحبار العملاق",
          ),
          QuestionModel(
            question: "أي حيوان يُمكنه تجديد أطرافه المقطوعة؟",
            options: ["🦎 السحلية", "⭐ نجم البحر", "🦎 السلمندر", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
        ];

      case 4: // طيور وحيوانات طائرة
        return [
          QuestionModel(
            question: "ما هو أسرع طائر في العالم؟",
            options: ["🦅 النسر", "🦅 الصقر الشاهين", "🦢 النعامة", "🐦 الطائر الطنان"],
            correctAnswer: "🦅 الصقر الشاهين",
          ),
          QuestionModel(
            question: "أي طائر لا يُمكنه الطيران؟",
            options: ["🐧 البطريق", "🦢 النعامة", "🥝 الكيوي", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
          QuestionModel(
            question: "ما هو الطائر الذي يُمكنه الطيران للخلف؟",
            options: ["🐦 الطائر الطنان", "🦜 الببغاء", "🦅 النسر", "🦅 الصقر"],
            correctAnswer: "🐦 الطائر الطنان",
          ),
          QuestionModel(
            question: "أي طائر يُعتبر رمز الحكمة؟",
            options: ["🦅 النسر", "🦉 البومة", "🐦‍⬛ الغراب", "🦅 الصقر"],
            correctAnswer: "🦉 البومة",
          ),
          QuestionModel(
            question: "ما هو أكبر طائر في العالم؟",
            options: ["🦅 النسر", "🦢 النعامة", "🦅 الكندور", "🦢 البجعة"],
            correctAnswer: "🦢 النعامة",
          ),
          QuestionModel(
            question: "أي طائر يُمكنه تقليد الأصوات البشرية؟",
            options: ["🦜 الببغاء", "🐦‍⬛ الغراب", "🐦 المينا", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
          QuestionModel(
            question: "ما هو الطائر الذي يُبني أعشاشاً جماعية؟",
            options: ["🦅 النسر", "🐦 العصفور", "🐦 طائر النساج", "🦉 البومة"],
            correctAnswer: "🐦 طائر النساج",
          ),
          QuestionModel(
            question: "أي طائر يُهاجر لأطول مسافة؟",
            options: ["🐦 الخطاف القطبي", "🐦 اللقلق", "🪿 الإوز", "🦢 البجع"],
            correctAnswer: "🐦 الخطاف القطبي",
          ),
          QuestionModel(
            question: "ما هو الطائر الذي يُضع بيضه في أعشاش طيور أخرى؟",
            options: ["🐦 الوقواق", "🐦‍⬛ الغراب", "🐦 العصفور", "🕊️ الحمام"],
            correctAnswer: "🐦 الوقواق",
          ),
          QuestionModel(
            question: "أي طائر له أطول جناحين؟",
            options: ["🦅 النسر", "🐦 القطرس", "🦅 الكندور", "🦢 البجع"],
            correctAnswer: "🐦 القطرس",
          ),
        ];

      case 5: // حشرات ومفصليات الأرجل
        return [
          QuestionModel(
            question: "كم عدد أرجل العنكبوت؟",
            options: ["6️⃣ 6", "8️⃣ 8", "🔟 10", "1️⃣2️⃣ 12"],
            correctAnswer: "8️⃣ 8",
          ),
          QuestionModel(
            question: "ما هو أقوى حيوان بالنسبة لحجمه؟",
            options: ["🐜 النملة", "🪲 الخنفساء", "🐝 النحلة", "🕷️ العنكبوت"],
            correctAnswer: "🪲 الخنفساء",
          ),
          QuestionModel(
            question: "أي حشرة تُنتج العسل؟",
            options: ["🐜 النملة", "🐝 النحلة", "🐝 الدبور", "🪰 الذبابة"],
            correctAnswer: "🐝 النحلة",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُمكنه حمل 50 ضعف وزنه؟",
            options: ["🐜 النملة", "🪲 الخنفساء", "🐝 النحلة", "🦗 الجندب"],
            correctAnswer: "🐜 النملة",
          ),
          QuestionModel(
            question: "أي حشرة تُعيش في مستعمرات منظمة؟",
            options: ["🐜 النملة", "🐝 النحل", "🐜 النمل الأبيض", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي يُضيء في الظلام؟",
            options: ["✨ اليراعة", "🪱 الدودة المضيئة", "🐟 بعض الأسماك", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
          QuestionModel(
            question: "أي حشرة تُمر بمراحل التحول الكامل؟",
            options: ["🦋 الفراشة", "🪲 الخنفساء", "🐝 النحلة", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
          QuestionModel(
            question: "ما هو أكبر حشرة في العالم؟",
            options: ["🪲 خنفساء جالوت", "🦋 عثة الأطلس", "🦗 حشرة العصا", "🦗 الجندب العملاق"],
            correctAnswer: "🪲 خنفساء جالوت",
          ),
          QuestionModel(
            question: "أي حشرة تُعتبر أخطر حشرة في العالم؟",
            options: ["🦟 البعوضة", "🐝 النحلة", "🕷️ العنكبوت", "🦂 العقرب"],
            correctAnswer: "🦟 البعوضة",
          ),
          QuestionModel(
            question: "ما هو الحيوان الذي له أكثر من قلب واحد؟",
            options: ["🐙 الأخطبوط", "🪱 دودة الأرض", "🦑 الحبار", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
        ];

      default: // مستويات متقدمة جداً (6-10)
        return _getExpertAnimalsQuestions(level);
    }
  }

  // الحصول على أسئلة الحيوانات الخبيرة للمستويات 6-10
  List<QuestionModel> _getExpertAnimalsQuestions(int level) {
    // أسئلة متقدمة جداً عن الحيوانات
    List<List<QuestionModel>> expertQuestions = [
      // المستوى 6: حيوانات منقرضة ونادرة
      [
        QuestionModel(
          question: "ما هو آخر حيوان ماموث مات؟",
          options: ["⏰ قبل 10000 سنة", "⏰ قبل 4000 سنة", "⏰ قبل 2000 سنة", "⏰ قبل 1000 سنة"],
          correctAnswer: "⏰ قبل 4000 سنة",
        ),
        QuestionModel(
          question: "أي حيوان يُعتبر أندر حيوان في العالم؟",
          options: ["🦏 وحيد القرن الأبيض", "🐼 الباندا العملاق", "🐷 خنزير البحر", "🐢 السلحفاة الذهبية"],
          correctAnswer: "🐢 السلحفاة الذهبية",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه البقاء حياً بدون رأس؟",
          options: ["🪳 الصرصور", "🐔 الدجاج", "🦎 السحلية", "🐍 الثعبان"],
          correctAnswer: "🪳 الصرصور",
        ),
        QuestionModel(
          question: "أي حيوان له أطول فترة حمل؟",
          options: ["🐘 الفيل", "🐋 الحوت", "🦒 الزرافة", "🦏 وحيد القرن"],
          correctAnswer: "🐘 الفيل",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُولد بحجم حبة الأرز؟",
          options: ["🐼 الباندا", "🦘 الكنغر", "🐨 الكوالا", "🐻‍❄️ الدب القطبي"],
          correctAnswer: "🐼 الباندا",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه النوم لمدة 3 سنوات؟",
          options: ["🐌 الحلزون", "🐻 الدب", "🦇 الخفاش", "🐢 السلحفاة"],
          correctAnswer: "🐌 الحلزون",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي له أكبر دماغ؟",
          options: ["🐘 الفيل", "🐋 الحوت الأزرق", "🐬 الدلفين", "👤 الإنسان"],
          correctAnswer: "🐋 الحوت الأزرق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تذوق الطعام بقدميه؟",
          options: ["🦋 الفراشة", "🐝 النحلة", "🪰 الذبابة", "🕷️ العنكبوت"],
          correctAnswer: "🦋 الفراشة",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي له أقوى ذاكرة؟",
          options: ["🐘 الفيل", "🐬 الدلفين", "🐵 الشمبانزي", "🐦‍⬛ الغراب"],
          correctAnswer: "🐘 الفيل",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه الضحك؟",
          options: ["🐒 القرد", "🐺 الضبع", "🐬 الدلفين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
      ],
      // المستوى 7: سلوكيات حيوانية معقدة
      [
        QuestionModel(
          question: "ما هو الحيوان الذي يُستخدم أدوات للصيد؟",
          options: ["🐵 الشمبانزي", "🐦‍⬛ الغراب", "🐬 الدلفين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمارس الزراعة؟",
          options: ["🐜 النمل الورقي", "🐝 النحل", "🦫 القندس", "🐿️ السنجاب"],
          correctAnswer: "🐜 النمل الورقي",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُعلم صغاره الصيد؟",
          options: ["🦁 الأسد", "🐅 النمر", "🐆 الفهد", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه التعرف على نفسه في المرآة؟",
          options: ["🐵 الشمبانزي", "🐬 الدلفين", "🐘 الفيل", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُقيم جنازات لموتاه؟",
          options: ["🐘 الفيل", "🐬 الدلفين", "🐵 الشمبانزي", "🐦‍⬛ الغراب"],
          correctAnswer: "🐘 الفيل",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه حل الألغاز المعقدة؟",
          options: ["🐦‍⬛ الغراب", "🦜 الببغاء", "🐬 الدلفين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُستخدم الطب الطبيعي؟",
          options: ["🐵 الشمبانزي", "🐻 الدب", "🐕 الكلب", "🐱 القط"],
          correctAnswer: "🐵 الشمبانزي",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تعلم لغة الإشارة؟",
          options: ["🐵 الشمبانزي", "🦍 الغوريلا", "🦧 الأورانجوتان", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُخطط للمستقبل؟",
          options: ["🐦‍⬛ الغراب", "🐿️ السنجاب", "🐝 النحل", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه الكذب والخداع؟",
          options: ["🐵 الشمبانزي", "🐬 الدلفين", "🐦‍⬛ الغراب", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
      ],
    ];

    // إرجاع الأسئلة حسب المستوى أو أسئلة عامة للمستويات العالية
    if (level >= 6 && level <= 7) {
      return expertQuestions[level - 6];
    }

    // للمستويات 8-15: أسئلة متنوعة صعبة ومتقدمة
    List<List<QuestionModel>> advancedQuestions = [
      // المستوى 8: علم الأحياء المتقدم
      [
        QuestionModel(
          question: "كم عدد أنواع الحيوانات المكتشفة في العالم؟",
          options: ["📊 حوالي 500,000", "📊 حوالي 1 مليون", "📊 حوالي 1.5 مليون", "📊 حوالي 2 مليون"],
          correctAnswer: "📊 حوالي 1.5 مليون",
        ),
        QuestionModel(
          question: "ما هو أصغر حيوان ثديي في العالم؟",
          options: ["🦇 الخفاش الطنان", "🐭 الفأر القزم", "🐭 الزبابة", "🐹 الهامستر القزم"],
          correctAnswer: "🦇 الخفاش الطنان",
        ),
        QuestionModel(
          question: "أي حيوان يُعيش أطول فترة؟",
          options: ["🐢 السلحفاة العملاقة", "🐋 الحوت الأزرق", "🦈 القرش الجرينلاندي", "🪸 شجرة المرجان"],
          correctAnswer: "🦈 القرش الجرينلاندي",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي له أكبر عدد من الأسنان؟",
          options: ["🦈 القرش", "🐊 التمساح", "🐌 الحلزون", "🐬 الدلفين"],
          correctAnswer: "🐌 الحلزون",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تغيير جنسه حسب الحاجة؟",
          options: ["🐠 السمك المهرج", "🐸 الضفدع", "🦎 بعض الزواحف", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُولد بدون جهاز مناعة؟",
          options: ["🐨 الكوالا", "🦘 الكنغر", "🐼 الباندا", "🐻‍❄️ الدب القطبي"],
          correctAnswer: "🐨 الكوالا",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه البقاء حياً في الفضاء؟",
          options: ["🐻 دب الماء", "🪳 الصرصور", "🐜 النملة", "🦠 البكتيريا"],
          correctAnswer: "🐻 دب الماء",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي له أكبر قلب؟",
          options: ["🐋 الحوت الأزرق", "🐘 الفيل", "🦒 الزرافة", "🐎 الحصان"],
          correctAnswer: "🐋 الحوت الأزرق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه إعادة نمو دماغه؟",
          options: ["⭐ نجم البحر", "🦎 السلمندر", "🪱 دودة البلاناريا", "🦑 الحبار"],
          correctAnswer: "🪱 دودة البلاناريا",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُعتبر خالداً بيولوجياً؟",
          options: ["🪼 قنديل البحر الخالد", "🐢 السلحفاة العملاقة", "🦈 القرش", "🐋 الحوت"],
          correctAnswer: "🪼 قنديل البحر الخالد",
        ),
      ],

      // المستوى 9: حيوانات الصحراء والقطب
      [
        QuestionModel(
          question: "أي حيوان يُمكنه البقاء بدون ماء لأطول فترة؟",
          options: ["🐪 الجمل", "🦎 السحلية الصحراوية", "🐭 الجربوع", "🦔 القنفذ الصحراوي"],
          correctAnswer: "🐭 الجربوع",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُغير لون فرائه حسب الفصول؟",
          options: ["🐻‍❄️ الدب القطبي", "🦊 الثعلب القطبي", "🐰 الأرنب القطبي", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان له أكبر أذنين نسبة لحجم جسمه؟",
          options: ["🐘 الفيل الأفريقي", "🦊 الفنك", "🐰 الأرنب", "🐭 الفأر"],
          correctAnswer: "🦊 الفنك",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُخزن الدهون في ذيله؟",
          options: ["🦎 الوزغة", "🐿️ السنجاب", "🐭 الجربوع", "🦔 القنفذ"],
          correctAnswer: "🦎 الوزغة",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تحمل درجة حرارة -40 مئوية؟",
          options: ["🐧 البطريق الإمبراطوري", "🐻‍❄️ الدب القطبي", "🦭 الفقمة", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُحفر أنفاقاً تحت الرمال؟",
          options: ["🐍 الأفعى الرملية", "🦎 السحلية الرملية", "🐭 الجربوع", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه شرب الماء المالح؟",
          options: ["🐪 الجمل", "🦭 الفقمة", "🐧 البطريق", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُهاجر أطول مسافة على الأرض؟",
          options: ["🦌 الرنة", "🦓 الحمار الوحشي", "🐘 الفيل الأفريقي", "🦏 وحيد القرن"],
          correctAnswer: "🦌 الرنة",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه الرؤية في الظلام الدامس؟",
          options: ["🦉 البومة", "🦇 الخفاش", "🐱 القط", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُبني بيوت الثلج؟",
          options: ["🐧 البطريق", "🐻‍❄️ الدب القطبي", "🦭 الفقمة", "🐺 الذئب القطبي"],
          correctAnswer: "🐧 البطريق",
        ),
      ],

      // المستوى 10: حيوانات الغابات المطيرة
      [
        QuestionModel(
          question: "أي حيوان يُعتبر أقرب الحيوانات للإنسان جينياً؟",
          options: ["🐵 الشمبانزي", "🦍 الغوريلا", "🦧 الأورانجوتان", "🐒 القرد"],
          correctAnswer: "🐵 الشمبانزي",
        ),
        QuestionModel(
          question: "ما هو أكبر حيوان في غابات الأمازون؟",
          options: ["🐆 الجاكوار", "🐊 التمساح الأمريكي", "🐍 الأناكوندا", "🦥 الكسلان العملاق"],
          correctAnswer: "🐊 التمساح الأمريكي",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه الطيران بدون أجنحة؟",
          options: ["🐿️ السنجاب الطائر", "🦎 الوزغة الطائرة", "🐸 الضفدع الطائر", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُقضي 22 ساعة يومياً نائماً؟",
          options: ["🦥 الكسلان", "🐨 الكوالا", "🦇 الخفاش", "🐻 الدب"],
          correctAnswer: "🐨 الكوالا",
        ),
        QuestionModel(
          question: "أي حيوان له أطول لسان نسبة لحجم جسمه؟",
          options: ["🦎 الحرباء", "🐸 الضفدع", "🐜 آكل النمل", "🦔 القنفذ"],
          correctAnswer: "🐜 آكل النمل",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُغير لونه حسب مزاجه؟",
          options: ["🦎 الحرباء", "🐙 الأخطبوط", "🦑 الحبار", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه المشي على الماء؟",
          options: ["🦎 سحلية الباسيليسك", "🕷️ العنكبوت المائي", "🐸 الضفدع", "🦗 الجندب المائي"],
          correctAnswer: "🦎 سحلية الباسيليسك",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُصدر أعلى صوت في الغابة؟",
          options: ["🐒 قرد الهاولر", "🐘 الفيل", "🦁 الأسد", "🐅 النمر"],
          correctAnswer: "🐒 قرد الهاولر",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تسلق الأشجار رأساً على عقب؟",
          options: ["🦥 الكسلان", "🐒 القرد", "🐿️ السنجاب", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُستخدم ذيله كيد خامسة؟",
          options: ["🐒 القرد العنكبوتي", "🦥 الكسلان", "🐿️ السنجاب", "🐆 الجاكوار"],
          correctAnswer: "🐒 القرد العنكبوتي",
        ),
      ],
    ];

    // إرجاع الأسئلة حسب المستوى
    if (level >= 8 && level <= 10) {
      return advancedQuestions[level - 8];
    }

    // للمستويات 11-15: أسئلة خبيرة متقدمة جداً
    return _getSuperExpertQuestions(level);
  }

  // أسئلة خبيرة للمستويات 11-15
  List<QuestionModel> _getSuperExpertQuestions(int level) {
    List<List<QuestionModel>> superExpertQuestions = [
      // المستوى 11: علم الوراثة والتطور
      [
        QuestionModel(
          question: "كم عدد الكروموسومات في الإنسان؟",
          options: ["🧬 44", "🧬 46", "🧬 48", "🧬 50"],
          correctAnswer: "🧬 46",
        ),
        QuestionModel(
          question: "أي حيوان يُشارك الإنسان في 98.8% من الحمض النووي؟",
          options: ["🐵 الشمبانزي", "🦍 الغوريلا", "🦧 الأورانجوتان", "🐒 البابون"],
          correctAnswer: "🐵 الشمبانزي",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه إعادة نمو أطرافه بالكامل؟",
          options: ["⭐ نجم البحر", "🦎 السلمندر", "🦀 السرطان", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان له أكبر جينوم (عدد الجينات)؟",
          options: ["🐭 الفأر", "👤 الإنسان", "🌾 نبات الأرز", "🦠 الأميبا"],
          correctAnswer: "🦠 الأميبا",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه العيش بدون أكسجين؟",
          options: ["🐢 السلحفاة المائية", "🐸 الضفدع", "🐟 السمك الرئوي", "🦠 البكتيريا اللاهوائية"],
          correctAnswer: "🦠 البكتيريا اللاهوائية",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تغيير جنسه عدة مرات في حياته؟",
          options: ["🐠 السمك الببغائي", "🐟 سمك الهامور", "🐠 السمك المهرج", "🦐 الجمبري"],
          correctAnswer: "🐠 السمك الببغائي",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي له أكبر عدد من الأعضاء الحسية؟",
          options: ["🕷️ العنكبوت", "🐙 الأخطبوط", "🦋 الفراشة", "🐍 الثعبان"],
          correctAnswer: "🐙 الأخطبوط",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه البقاء مجمداً ثم العودة للحياة؟",
          options: ["🐸 ضفدع الخشب", "🐻 دب الماء", "🐛 يرقة البعوض", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه رؤية الأشعة فوق البنفسجية؟",
          options: ["🐝 النحل", "🦋 الفراشة", "🐦 الطيور", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان له أكبر دماغ نسبة لحجم جسمه؟",
          options: ["🐬 الدلفين", "🐙 الأخطبوط", "🐦‍⬛ الغراب", "🐜 النملة"],
          correctAnswer: "🐜 النملة",
        ),
      ],

      // المستوى 12: حيوانات ما قبل التاريخ
      [
        QuestionModel(
          question: "كم سنة عاشت الديناصورات على الأرض؟",
          options: ["⏰ 100 مليون سنة", "⏰ 165 مليون سنة", "⏰ 200 مليون سنة", "⏰ 250 مليون سنة"],
          correctAnswer: "⏰ 165 مليون سنة",
        ),
        QuestionModel(
          question: "ما هو أكبر ديناصور آكل لحوم؟",
          options: ["🦕 التيرانوصور", "🦕 السبينوصور", "🦕 الجيجانوتوصور", "🦕 الكاركارودونتوصور"],
          correctAnswer: "🦕 السبينوصور",
        ),
        QuestionModel(
          question: "أي حيوان عاصر الديناصورات ولا يزال حياً؟",
          options: ["🐊 التمساح", "🦈 القرش", "🐢 السلحفاة", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو أصغر ديناصور مكتشف؟",
          options: ["🦕 الميكرورابتور", "🦕 الكومبسوجناثوس", "🦕 الإيبيدكسيبتريكس", "🦕 الأنكيورنيس"],
          correctAnswer: "🦕 الميكرورابتور",
        ),
        QuestionModel(
          question: "أي حيوان منقرض كان أكبر من الحوت الأزرق؟",
          options: ["🦕 الأرجنتينوصور", "🐋 الليفياثان", "🦕 البراكيوصور", "🦈 الميجالودون"],
          correctAnswer: "🦕 الأرجنتينوصور",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي انقرض بسبب البشر مؤخراً؟",
          options: ["🦤 طائر الدودو", "🐅 النمر التسماني", "🦏 وحيد القرن الأبيض", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي ديناصور كان له أطول رقبة؟",
          options: ["🦕 البراكيوصور", "🦕 الديبلودوكوس", "🦕 الماميناشيصور", "🦕 الأرجنتينوصور"],
          correctAnswer: "🦕 الماميناشيصور",
        ),
        QuestionModel(
          question: "ما هو أول حيوان طار في التاريخ؟",
          options: ["🦋 الحشرات", "🦕 البتيروصور", "🐦 الطيور", "🦇 الخفافيش"],
          correctAnswer: "🦋 الحشرات",
        ),
        QuestionModel(
          question: "أي حيوان منقرض كان له أكبر أسنان؟",
          options: ["🦈 الميجالودون", "🐘 الماموث", "🦕 التيرانوصور", "🐅 النمر ذو الأسنان السيفية"],
          correctAnswer: "🦈 الميجالودون",
        ),
        QuestionModel(
          question: "ما هو آخر حيوان ماموث مات؟",
          options: ["⏰ قبل 10,000 سنة", "⏰ قبل 4,000 سنة", "⏰ قبل 2,000 سنة", "⏰ قبل 1,000 سنة"],
          correctAnswer: "⏰ قبل 4,000 سنة",
        ),
      ],

      // المستوى 13: حيوانات الأعماق والفضاء
      [
        QuestionModel(
          question: "ما هو أعمق مكان وُجدت فيه حياة على الأرض؟",
          options: ["🌊 11 كيلومتر", "🌊 15 كيلومتر", "🌊 20 كيلومتر", "🌊 25 كيلومتر"],
          correctAnswer: "🌊 11 كيلومتر",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تحمل ضغط أعماق المحيط؟",
          options: ["🐟 السمك الشفاف", "🦑 الحبار العملاق", "🦐 الجمبري العميق", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُضيء في أعماق المحيط؟",
          options: ["🐟 سمك الصياد", "🪼 قنديل البحر", "🦑 الحبار المضيء", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان أُرسل إلى الفضاء أولاً؟",
          options: ["🐕 الكلب لايكا", "🐒 القرد هام", "🪰 ذباب الفاكهة", "🐭 الفئران"],
          correctAnswer: "🪰 ذباب الفاكهة",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه البقاء في الفضاء الخارجي؟",
          options: ["🐻 دب الماء", "🪳 الصرصور", "🦠 البكتيريا", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تحمل الإشعاع النووي؟",
          options: ["🪳 الصرصور", "🐻 دب الماء", "🦠 البكتيريا", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو أكبر حيوان في أعماق المحيط؟",
          options: ["🦑 الحبار العملاق", "🐋 الحوت الأزرق", "🦈 القرش الأبيض", "🐙 الأخطبوط العملاق"],
          correctAnswer: "🦑 الحبار العملاق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه العيش بدون ضوء الشمس؟",
          options: ["🐟 أسماك الأعماق", "🪱 الديدان الأنبوبية", "🦐 الجمبري الكيميائي", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه تحمل درجة حرارة 100 مئوية؟",
          options: ["🦠 البكتيريا الحرارية", "🐻 دب الماء", "🪱 الديدان الحرارية", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه البقاء بدون طعام لسنوات؟",
          options: ["🐻 دب الماء", "🐸 الضفدع الصحراوي", "🐌 الحلزون", "🕷️ العنكبوت"],
          correctAnswer: "🐻 دب الماء",
        ),
      ],

      // المستوى 14: الذكاء الاصطناعي والحيوانات
      [
        QuestionModel(
          question: "أي حيوان يُمكنه استخدام الأدوات بطريقة معقدة؟",
          options: ["🐵 الشمبانزي", "🐦‍⬛ الغراب", "🐬 الدلفين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه حل المتاهات المعقدة؟",
          options: ["🐭 الفأر", "🐙 الأخطبوط", "🐦‍⬛ الغراب", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تعلم لغة الإشارة؟",
          options: ["🐵 الشمبانزي", "🦍 الغوريلا", "🐬 الدلفين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه التعرف على نفسه في المرآة؟",
          options: ["🐘 الفيل", "🐬 الدلفين", "🐵 الشمبانزي", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه عد الأرقام؟",
          options: ["🐦‍⬛ الغراب", "🐵 الشمبانزي", "🐬 الدلفين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه التخطيط للمستقبل؟",
          options: ["🐦‍⬛ الغراب", "🐿️ السنجاب", "🐝 النحل", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه تعليم صغاره مهارات معقدة؟",
          options: ["🐬 الدلفين", "🐘 الفيل", "🐵 الشمبانزي", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه استخدام الطب الطبيعي؟",
          options: ["🐵 الشمبانزي", "🐘 الفيل", "🐻 الدب", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه الكذب والخداع؟",
          options: ["🐵 الشمبانزي", "🐦‍⬛ الغراب", "🐬 الدلفين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه فهم المفاهيم المجردة؟",
          options: ["🐦‍⬛ الغراب", "🐵 الشمبانزي", "🐬 الدلفين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
      ],

      // المستوى 15: أسرار الطبيعة الخفية
      [
        QuestionModel(
          question: "كم عدد الحيوانات غير المكتشفة المتوقعة؟",
          options: ["📊 5 مليون", "📊 8 مليون", "📊 12 مليون", "📊 20 مليون"],
          correctAnswer: "📊 8 مليون",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه السفر عبر الزمن بيولوجياً؟",
          options: ["🪼 قنديل البحر الخالد", "🐻 دب الماء", "🦠 البكتيريا المتجمدة", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه تغيير قوانين الفيزياء؟",
          options: ["🦎 الوزغة (الجاذبية)", "🐟 السمك الكهربائي", "🦇 الخفاش (الصدى)", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه رؤية المجالات المغناطيسية؟",
          options: ["🐦 الطيور المهاجرة", "🐢 السلاحف البحرية", "🦈 القرش", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه التنبؤ بالزلازل؟",
          options: ["🐕 الكلب", "🐍 الثعبان", "🐎 الحصان", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه إنتاج الكهرباء؟",
          options: ["🐟 السمك الكهربائي", "🦈 الراي الكهربائي", "🐍 ثعبان البحر الكهربائي", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه العيش في عوالم متعددة؟",
          options: ["🐸 الضفدع (ماء وبر)", "🐧 البطريق (ماء وجليد)", "🦇 الخفاش (هواء وكهوف)", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يُمكنه التحكم في الطقس؟",
          options: ["🐝 النحل (التلقيح)", "🦋 الفراشة (تأثير الفراشة)", "🐋 الحوت (التيارات)", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو الحيوان الذي يُمكنه خلق حياة جديدة؟",
          options: ["🐜 النملة (الزراعة)", "🐝 النحل (التلقيح)", "🪱 دودة الأرض (التربة)", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "أي حيوان يحمل أسرار الكون؟",
          options: ["🧬 الحمض النووي", "🧠 الدماغ", "👁️ العين", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
      ],
    ];

    // إرجاع الأسئلة حسب المستوى (11-15)
    if (level >= 11 && level <= 15) {
      return superExpertQuestions[level - 11];
    }

    // إرجاع أسئلة افتراضية للمستويات غير المحددة
    return superExpertQuestions[0];
  }

  // الحصول على أسئلة النباتات حسب المستوى
  List<QuestionModel> _getPlantsQuestions(int level) {
    switch (level) {
      case 1: // نباتات أساسية ومعروفة
        return [
          QuestionModel(
            question: "ما هو الجزء الذي يقوم بعملية البناء الضوئي في النبات؟",
            options: ["🌿 الأوراق", "🌱 الجذور", "🌸 الأزهار", "🌳 الساق"],
            correctAnswer: "🌿 الأوراق",
          ),
          QuestionModel(
            question: "أي من هذه النباتات يُعتبر من الخضروات الورقية؟",
            options: ["🥬 الخس", "🥕 الجزر", "🍅 الطماطم", "🥔 البطاطس"],
            correctAnswer: "🥬 الخس",
          ),
          QuestionModel(
            question: "ما هو النبات الذي يُستخدم لصنع الخبز؟",
            options: ["🌾 القمح", "🌽 الذرة", "🌱 الشعير", "🌾 الأرز"],
            correctAnswer: "🌾 القمح",
          ),
          QuestionModel(
            question: "أي من هذه الفواكه تنمو على الأشجار؟",
            options: ["🍎 التفاح", "🍓 الفراولة", "🍇 العنب", "🍉 البطيخ"],
            correctAnswer: "🍎 التفاح",
          ),
          QuestionModel(
            question: "ما هو لون الكلوروفيل في النباتات؟",
            options: ["🟢 أخضر", "🔴 أحمر", "🟡 أصفر", "🔵 أزرق"],
            correctAnswer: "🟢 أخضر",
          ),
          QuestionModel(
            question: "أي من هذه النباتات يُعتبر من التوابل؟",
            options: ["🌿 البقدونس", "🧄 الثوم", "🧅 البصل", "✨ جميع ما سبق"],
            correctAnswer: "✨ جميع ما سبق",
          ),
          QuestionModel(
            question: "ما هو الجزء الذي نأكله من نبات الجزر؟",
            options: ["🌱 الجذر", "🌿 الأوراق", "🌸 الزهرة", "🌰 الثمرة"],
            correctAnswer: "🌱 الجذر",
          ),
          QuestionModel(
            question: "أي من هذه النباتات يحتاج إلى الكثير من الماء؟",
            options: ["🌾 الأرز", "🌵 الصبار", "🌲 الصنوبر", "🌿 اللافندر"],
            correctAnswer: "🌾 الأرز",
          ),
          QuestionModel(
            question: "ما هو النبات الذي يُستخدم لصنع السكر؟",
            options: ["🌾 قصب السكر", "🌽 الذرة", "🌱 الشعير", "🌾 القمح"],
            correctAnswer: "🌾 قصب السكر",
          ),
          QuestionModel(
            question: "أي من هذه الورود له رائحة عطرة قوية؟",
            options: ["🌹 الورد الجوري", "🌻 عباد الشمس", "🌷 التوليب", "🌺 الياسمين"],
            correctAnswer: "🌹 الورد الجوري",
          ),
        ];

      case 2: // خضروات وفواكه
        return [
          QuestionModel(
            question: "أي من هذه الخضروات غنية بفيتامين أ؟",
            options: ["🥕 الجزر", "🥬 الخس", "🥒 الخيار", "🧅 البصل"],
            correctAnswer: "🥕 الجزر",
          ),
          QuestionModel(
            question: "ما هي الفاكهة التي تحتوي على أكبر كمية من فيتامين سي؟",
            options: ["🍊 البرتقال", "🥝 الكيوي", "🍓 الفراولة", "🍋 الليمون"],
            correctAnswer: "🥝 الكيوي",
          ),
          QuestionModel(
            question: "أي من هذه الخضروات تنمو تحت الأرض؟",
            options: ["🥔 البطاطس", "🍅 الطماطم", "🥬 الخس", "🌶️ الفلفل"],
            correctAnswer: "🥔 البطاطس",
          ),
          QuestionModel(
            question: "ما هو الموسم الذي تنضج فيه فاكهة التفاح؟",
            options: ["🍂 الخريف", "🌸 الربيع", "☀️ الصيف", "❄️ الشتاء"],
            correctAnswer: "🍂 الخريف",
          ),
          QuestionModel(
            question: "أي من هذه الفواكه تحتوي على البذور من الخارج؟",
            options: ["🍓 الفراولة", "🍎 التفاح", "🍊 البرتقال", "🍌 الموز"],
            correctAnswer: "🍓 الفراولة",
          ),
          QuestionModel(
            question: "ما هو الخضار الذي يُطلق عليه 'الذهب الأخضر'؟",
            options: ["🥑 الأفوكادو", "🥒 الخيار", "🥬 السبانخ", "🥦 البروكلي"],
            correctAnswer: "🥑 الأفوكادو",
          ),
          QuestionModel(
            question: "أي من هذه الخضروات تنتمي لعائلة الكرنب؟",
            options: ["🥦 البروكلي", "🥕 الجزر", "🍅 الطماطم", "🧅 البصل"],
            correctAnswer: "🥦 البروكلي",
          ),
          QuestionModel(
            question: "ما هي الفاكهة التي تُسمى 'ملكة الفواكه'؟",
            options: ["🍓 الفراولة", "🍎 التفاح", "🥭 المانجو", "🍇 العنب"],
            correctAnswer: "🍓 الفراولة",
          ),
          QuestionModel(
            question: "أي من هذه الخضروات يحتوي على الكثير من الماء؟",
            options: ["🥒 الخيار", "🥔 البطاطس", "🥕 الجزر", "🧄 الثوم"],
            correctAnswer: "🥒 الخيار",
          ),
          QuestionModel(
            question: "ما هو النبات الذي يُستخدم لصنع زيت الزيتون؟",
            options: ["🫒 الزيتون", "🥥 جوز الهند", "🌻 عباد الشمس", "🌰 الجوز"],
            correctAnswer: "🫒 الزيتون",
          ),
        ];

      case 3: // أشجار وغابات
        return [
          QuestionModel(
            question: "ما هو أطول شجرة في العالم؟",
            options: ["🌲 السيكويا", "🌳 البلوط", "🌴 النخيل", "🌲 الصنوبر"],
            correctAnswer: "🌲 السيكويا",
          ),
          QuestionModel(
            question: "أي من هذه الأشجار تفقد أوراقها في الشتاء؟",
            options: ["🍂 البلوط", "🌲 الصنوبر", "🌴 النخيل", "🌿 الزيتون"],
            correctAnswer: "🍂 البلوط",
          ),
          QuestionModel(
            question: "ما هو الشجر الذي يُنتج المطاط الطبيعي؟",
            options: ["🌳 شجرة المطاط", "🌴 النخيل", "🌲 الصنوبر", "🍂 البلوط"],
            correctAnswer: "🌳 شجرة المطاط",
          ),
          QuestionModel(
            question: "أي من هذه الأشجار تنمو في الصحراء؟",
            options: ["🌴 النخيل", "🍂 البلوط", "🌲 الصنوبر", "🌳 الزان"],
            correctAnswer: "🌴 النخيل",
          ),
          QuestionModel(
            question: "ما هو عمر أقدم شجرة في العالم تقريباً؟",
            options: ["⏰ 1000 سنة", "⏰ 3000 سنة", "⏰ 5000 سنة", "⏰ 9000 سنة"],
            correctAnswer: "⏰ 9000 سنة",
          ),
          QuestionModel(
            question: "أي من هذه الأشجار يُستخدم خشبها في صناعة الأثاث؟",
            options: ["🌳 البلوط", "🌴 النخيل", "🌵 الصبار", "🌿 الخيزران"],
            correctAnswer: "🌳 البلوط",
          ),
          QuestionModel(
            question: "ما هو الشجر الذي يُنتج ثمار التمر؟",
            options: ["🌴 النخيل", "🌳 التين", "🍂 البلوط", "🌲 الصنوبر"],
            correctAnswer: "🌴 النخيل",
          ),
          QuestionModel(
            question: "أي من هذه الأشجار دائمة الخضرة؟",
            options: ["🌲 الصنوبر", "🍂 القيقب", "🌳 البتولا", "🍃 الحور"],
            correctAnswer: "🌲 الصنوبر",
          ),
          QuestionModel(
            question: "ما هو الشجر الذي يُنتج ثمار الكاكاو؟",
            options: ["🌳 شجرة الكاكاو", "🌴 النخيل", "🌲 الصنوبر", "🍂 البلوط"],
            correctAnswer: "🌳 شجرة الكاكاو",
          ),
          QuestionModel(
            question: "أي من هذه الأشجار تنمو بسرعة كبيرة؟",
            options: ["🌿 الخيزران", "🌳 البلوط", "🌲 الأرز", "🌴 النخيل"],
            correctAnswer: "🌿 الخيزران",
          ),
        ];

      case 4: // ورود وأزهار
        return [
          QuestionModel(
            question: "ما هو الوقت المناسب لزراعة الورود؟",
            options: ["🌸 الربيع", "☀️ الصيف", "🍂 الخريف", "❄️ الشتاء"],
            correctAnswer: "🌸 الربيع",
          ),
          QuestionModel(
            question: "أي من هذه الورود يُعتبر رمز الحب؟",
            options: ["🌹 الورد الأحمر", "🌻 عباد الشمس", "🌷 التوليب", "🌺 الياسمين"],
            correctAnswer: "🌹 الورد الأحمر",
          ),
          QuestionModel(
            question: "ما هو الزهر الذي يتبع الشمس في حركتها؟",
            options: ["🌻 عباد الشمس", "🌹 الورد", "🌷 التوليب", "🌺 الياسمين"],
            correctAnswer: "🌻 عباد الشمس",
          ),
          QuestionModel(
            question: "أي من هذه الأزهار تنمو من البصيلات؟",
            options: ["🌷 التوليب", "🌹 الورد", "🌻 عباد الشمس", "🌺 الياسمين"],
            correctAnswer: "🌷 التوليب",
          ),
          QuestionModel(
            question: "ما هو الزهر الوطني لهولندا؟",
            options: ["🌷 التوليب", "🌹 الورد", "🌻 عباد الشمس", "🌺 اللوتس"],
            correctAnswer: "🌷 التوليب",
          ),
          QuestionModel(
            question: "أي من هذه الأزهار له رائحة عطرة قوية في المساء؟",
            options: ["🌺 الياسمين", "🌻 عباد الشمس", "🌷 التوليب", "🌼 الأقحوان"],
            correctAnswer: "🌺 الياسمين",
          ),
          QuestionModel(
            question: "ما هو الزهر الذي يُستخدم في صناعة العطور؟",
            options: ["🌹 الورد", "🌻 عباد الشمس", "🌷 التوليب", "✨ جميع ما سبق"],
            correctAnswer: "🌹 الورد",
          ),
          QuestionModel(
            question: "أي من هذه الأزهار تتفتح في الليل؟",
            options: ["🌙 زهرة القمر", "🌻 عباد الشمس", "🌷 التوليب", "🌼 الأقحوان"],
            correctAnswer: "🌙 زهرة القمر",
          ),
          QuestionModel(
            question: "ما هو الزهر الذي يُرمز للصداقة؟",
            options: ["🌻 عباد الشمس", "🌹 الورد الأحمر", "🌷 التوليب", "🌺 الياسمين"],
            correctAnswer: "🌻 عباد الشمس",
          ),
          QuestionModel(
            question: "أي من هذه الأزهار يُستخدم في الطب الشعبي؟",
            options: ["🌼 البابونج", "🌻 عباد الشمس", "🌷 التوليب", "🌺 الياسمين"],
            correctAnswer: "🌼 البابونج",
          ),
        ];

      case 5: // نباتات طبية وعطرية
        return [
          QuestionModel(
            question: "أي من هذه النباتات يُستخدم لعلاج نزلات البرد؟",
            options: ["🌿 النعناع", "🌵 الصبار", "🌻 عباد الشمس", "🌷 التوليب"],
            correctAnswer: "🌿 النعناع",
          ),
          QuestionModel(
            question: "ما هو النبات الذي يُستخدم لعلاج الحروق؟",
            options: ["🌵 الصبار", "🌿 النعناع", "🌹 الورد", "🌻 عباد الشمس"],
            correctAnswer: "🌵 الصبار",
          ),
          QuestionModel(
            question: "أي من هذه النباتات يُستخدم كمهدئ طبيعي؟",
            options: ["🌼 البابونج", "🌶️ الفلفل", "🧄 الثوم", "🧅 البصل"],
            correctAnswer: "🌼 البابونج",
          ),
          QuestionModel(
            question: "ما هو النبات الذي يُستخدم لتقوية المناعة؟",
            options: ["🧄 الثوم", "🥒 الخيار", "🥬 الخس", "🥔 البطاطس"],
            correctAnswer: "🧄 الثوم",
          ),
          QuestionModel(
            question: "أي من هذه النباتات يُستخدم لعلاج مشاكل الهضم؟",
            options: ["🌿 الزنجبيل", "🌵 الصبار", "🌻 عباد الشمس", "🌷 التوليب"],
            correctAnswer: "🌿 الزنجبيل",
          ),
          QuestionModel(
            question: "ما هو النبات الذي يُستخدم لصنع الشاي المهدئ؟",
            options: ["🌿 اللافندر", "🌶️ الفلفل", "🧅 البصل", "🥔 البطاطس"],
            correctAnswer: "🌿 اللافندر",
          ),
          QuestionModel(
            question: "أي من هذه النباتات يُستخدم لعلاج الصداع؟",
            options: ["🌿 إكليل الجبل", "🥒 الخيار", "🥬 الخس", "🥔 البطاطس"],
            correctAnswer: "🌿 إكليل الجبل",
          ),
          QuestionModel(
            question: "ما هو النبات الذي يُستخدم لتنظيف الأسنان طبيعياً؟",
            options: ["🌿 السواك", "🌵 الصبار", "🌻 عباد الشمس", "🌷 التوليب"],
            correctAnswer: "🌿 السواك",
          ),
          QuestionModel(
            question: "أي من هذه النباتات يُستخدم لعلاج السعال؟",
            options: ["🍯 العسل والليمون", "🌶️ الفلفل", "🧅 البصل", "🥔 البطاطس"],
            correctAnswer: "🍯 العسل والليمون",
          ),
          QuestionModel(
            question: "ما هو النبات الذي يُستخدم لطرد الحشرات؟",
            options: ["🌿 النعناع", "🥒 الخيار", "🥬 الخس", "🥔 البطاطس"],
            correctAnswer: "🌿 النعناع",
          ),
        ];

      default:
        // للمستويات 6-10: أسئلة متقدمة عن النباتات
        return _getAdvancedPlantsQuestions(level);
    }
  }

  // أسئلة متقدمة للنباتات (المستويات 6-10)
  List<QuestionModel> _getAdvancedPlantsQuestions(int level) {
    List<List<QuestionModel>> advancedQuestions = [
      // المستوى 6: نباتات نادرة ومهددة بالانقراض
      [
        QuestionModel(
          question: "ما هو النبات الذي يُطلق عليه 'شجرة الحياة'؟",
          options: ["🌳 الباوباب", "🌴 النخيل", "🌲 الصنوبر", "🍂 البلوط"],
          correctAnswer: "🌳 الباوباب",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُعتبر من أندر النباتات في العالم؟",
          options: ["🌺 زهرة الجثة", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌺 زهرة الجثة",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يعيش أكثر من 1000 سنة؟",
          options: ["🌲 شجرة الأرز", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌲 شجرة الأرز",
        ),
        QuestionModel(
          question: "أي من هذه النباتات آكل للحوم؟",
          options: ["🪴 نبات الجرة", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🪴 نبات الجرة",
        ),
        QuestionModel(
          question: "ما هو النبات الذي ينمو في أقسى الظروف؟",
          options: ["🌵 الصبار", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌵 الصبار",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُضيء في الظلام؟",
          options: ["🍄 الفطر المضيء", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🍄 الفطر المضيء",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه العيش بدون تربة؟",
          options: ["🌿 النباتات الهوائية", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 النباتات الهوائية",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه تنقية الهواء؟",
          options: ["🌿 نبات العنكبوت", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات العنكبوت",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُستخدم لصنع الورق؟",
          options: ["🌳 شجرة الأوكالبتوس", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌳 شجرة الأوكالبتوس",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه امتصاص المعادن الثقيلة؟",
          options: ["🌿 عباد الشمس", "🌻 الذرة", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 عباد الشمس",
        ),
      ],

      // المستوى 7: علم النبات المتقدم
      [
        QuestionModel(
          question: "كم عدد أنواع النباتات المكتشفة في العالم؟",
          options: ["📊 حوالي 200,000", "📊 حوالي 400,000", "📊 حوالي 600,000", "📊 حوالي 800,000"],
          correctAnswer: "📊 حوالي 400,000",
        ),
        QuestionModel(
          question: "ما هو أكبر نبات في العالم؟",
          options: ["🌳 شجرة السيكويا العملاقة", "🌴 النخيل", "🌲 الصنوبر", "🍂 البلوط"],
          correctAnswer: "🌳 شجرة السيكويا العملاقة",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه التكاثر بدون بذور؟",
          options: ["🍄 الفطريات", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🍄 الفطريات",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُنتج أكبر كمية من الأكسجين؟",
          options: ["🌊 الطحالب البحرية", "🌳 الأشجار", "🌿 الأعشاب", "🌻 عباد الشمس"],
          correctAnswer: "🌊 الطحالب البحرية",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه العيش في الماء المالح؟",
          options: ["🌿 نباتات المانجروف", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نباتات المانجروف",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه تحمل درجات حرارة تحت الصفر؟",
          options: ["🌲 الصنوبر القطبي", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌲 الصنوبر القطبي",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه التواصل مع النباتات الأخرى؟",
          options: ["🌳 الأشجار", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌳 الأشجار",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه إنتاج الكهرباء؟",
          options: ["🌿 نبات الميموزا", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات الميموزا",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه تغيير لونه؟",
          options: ["🌺 الكوبية", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌺 الكوبية",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه العد والحساب؟",
          options: ["🌿 نبات الفينوس", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات الفينوس",
        ),
      ],

      // المستوى 8: نباتات الفضاء والمستقبل
      [
        QuestionModel(
          question: "أي من هذه النباتات نمت في الفضاء؟",
          options: ["🌱 الفجل", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌱 الفجل",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه تنظيف المياه الملوثة؟",
          options: ["🌿 نبات الخس المائي", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات الخس المائي",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه إنتاج الوقود الحيوي؟",
          options: ["🌾 الذرة", "🌻 عباد الشمس", "🌷 التوليب", "✨ جميع ما سبق"],
          correctAnswer: "✨ جميع ما سبق",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه امتصاص الإشعاع؟",
          options: ["🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد", "🌿 الطحالب"],
          correctAnswer: "🌻 عباد الشمس",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه النمو في المريخ؟",
          options: ["🥔 البطاطس", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🥔 البطاطس",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه إنتاج البلاستيك الحيوي؟",
          options: ["🌽 الذرة", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌽 الذرة",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه تحويل ثاني أكسيد الكربون إلى أكسجين بكفاءة عالية؟",
          options: ["🌿 الطحالب الدقيقة", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 الطحالب الدقيقة",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه إنتاج الأدوية؟",
          options: ["🌿 التبغ المعدل وراثياً", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 التبغ المعدل وراثياً",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه النمو بدون ضوء الشمس؟",
          options: ["🍄 الفطريات", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🍄 الفطريات",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه إنتاج الطعام في المختبر؟",
          options: ["🥬 الخس المزروع مختبرياً", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🥬 الخس المزروع مختبرياً",
        ),
      ],

      // المستوى 9: أسرار النباتات الخفية
      [
        QuestionModel(
          question: "كم عدد النباتات غير المكتشفة المتوقعة؟",
          options: ["📊 50,000", "📊 100,000", "📊 200,000", "📊 500,000"],
          correctAnswer: "📊 200,000",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه التنبؤ بالطقس؟",
          options: ["🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد", "🌿 نبات الحساسية"],
          correctAnswer: "🌿 نبات الحساسية",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه الشعور بالألم؟",
          options: ["🌿 نبات الميموزا", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات الميموزا",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه تذكر الأحداث؟",
          options: ["🌿 نبات الفينوس", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات الفينوس",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه إنتاج الموسيقى؟",
          options: ["🌿 نبات الخيزران", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات الخيزران",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه التحكم في الحشرات؟",
          options: ["🌿 النباتات آكلة الحشرات", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 النباتات آكلة الحشرات",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه تغيير شكله؟",
          options: ["🌿 نبات المحاكاة", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات المحاكاة",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه إرسال إشارات كيميائية؟",
          options: ["🌳 الأشجار", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌳 الأشجار",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه العيش إلى الأبد؟",
          options: ["🌿 نبات الخلود", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 نبات الخلود",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه التحكم في الجاذبية؟",
          options: ["🌿 النباتات الفضائية", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 النباتات الفضائية",
        ),
      ],

      // المستوى 10: عجائب عالم النباتات
      [
        QuestionModel(
          question: "ما هو النبات الذي يحمل أسرار الحياة؟",
          options: ["🧬 الحمض النووي النباتي", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🧬 الحمض النووي النباتي",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه السفر عبر الزمن؟",
          options: ["🌰 البذور القديمة", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌰 البذور القديمة",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه خلق عوالم جديدة؟",
          options: ["🌍 النظم البيئية", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌍 النظم البيئية",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه التحكم في المناخ؟",
          options: ["🌳 الغابات", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌳 الغابات",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه إنقاذ الكوكب؟",
          options: ["🌱 جميع النباتات", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌱 جميع النباتات",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه التواصل مع الكون؟",
          options: ["🌌 النباتات الكونية", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌌 النباتات الكونية",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه إنتاج الحياة؟",
          options: ["🌱 البذرة", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌱 البذرة",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يُمكنه تحويل الصحراء إلى جنة؟",
          options: ["🌿 النباتات الصحراوية", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌿 النباتات الصحراوية",
        ),
        QuestionModel(
          question: "ما هو النبات الذي يُمكنه شفاء الأرض؟",
          options: ["🌍 النباتات المعالجة", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "🌍 النباتات المعالجة",
        ),
        QuestionModel(
          question: "أي من هذه النباتات يحمل مستقبل البشرية؟",
          options: ["✨ جميع النباتات", "🌻 عباد الشمس", "🌷 التوليب", "🌹 الورد"],
          correctAnswer: "✨ جميع النباتات",
        ),
      ],
    ];

    // إرجاع الأسئلة حسب المستوى (6-10)
    if (level >= 6 && level <= 10) {
      return advancedQuestions[level - 6];
    }

    // إرجاع أسئلة افتراضية للمستويات غير المحددة
    return advancedQuestions[0];
  }

  // الحصول على أسئلة السيارات حسب المستوى
  List<QuestionModel> _getCarsQuestions(int level) {
    switch (level) {
      case 1: // مستوى سهل - أساسيات السيارات
        return [
          QuestionModel(
            question: "ما هو الجزء الذي يُحرك السيارة؟",
            options: ["🔧 المحرك", "🛞 العجلات", "🪟 النوافذ", "💺 المقاعد"],
            correctAnswer: "🔧 المحرك",
          ),
          QuestionModel(
            question: "كم عدد العجلات في السيارة العادية؟",
            options: ["2️⃣ اثنان", "3️⃣ ثلاثة", "4️⃣ أربعة", "6️⃣ ستة"],
            correctAnswer: "4️⃣ أربعة",
          ),
          QuestionModel(
            question: "ما هو الوقود الأكثر استخداماً في السيارات؟",
            options: ["⛽ البنزين", "🔋 الكهرباء", "💨 الهواء", "💧 الماء"],
            correctAnswer: "⛽ البنزين",
          ),
          QuestionModel(
            question: "أي جزء يُستخدم لإيقاف السيارة؟",
            options: ["🚗 المقود", "🦶 الفرامل", "🔧 المحرك", "🛞 العجلات"],
            correctAnswer: "🦶 الفرامل",
          ),
          QuestionModel(
            question: "ما هو الجزء الذي يُوجه السيارة؟",
            options: ["🚗 المقود", "🦶 الفرامل", "🔧 المحرك", "💺 المقعد"],
            correctAnswer: "🚗 المقود",
          ),
          QuestionModel(
            question: "أي لون يعني 'قف' في إشارة المرور؟",
            options: ["🔴 الأحمر", "🟡 الأصفر", "🟢 الأخضر", "🔵 الأزرق"],
            correctAnswer: "🔴 الأحمر",
          ),
          QuestionModel(
            question: "ما هو الجزء الذي يُضيء الطريق ليلاً؟",
            options: ["💡 المصابيح الأمامية", "🪟 النوافذ", "🚗 المقود", "💺 المقاعد"],
            correctAnswer: "💡 المصابيح الأمامية",
          ),
          QuestionModel(
            question: "أين يجلس السائق في السيارة؟",
            options: ["👈 الأمام يسار", "👉 الأمام يمين", "👆 الخلف", "👇 الوسط"],
            correctAnswer: "👈 الأمام يسار",
          ),
          QuestionModel(
            question: "ما هو الجزء الذي يحمي من المطر؟",
            options: ["☂️ السقف", "🛞 العجلات", "🔧 المحرك", "🦶 الفرامل"],
            correctAnswer: "☂️ السقف",
          ),
          QuestionModel(
            question: "أي جزء يُصدر صوت التنبيه؟",
            options: ["📯 الكلاكس", "🔧 المحرك", "🛞 العجلات", "🚗 المقود"],
            correctAnswer: "📯 الكلاكس",
          ),
        ];

      case 2: // مستوى سهل-متوسط - أجزاء السيارة
        return [
          QuestionModel(
            question: "ما هو الجزء الذي يُبرد المحرك؟",
            options: ["❄️ الرادياتير", "🔥 العادم", "⛽ خزان الوقود", "🔋 البطارية"],
            correctAnswer: "❄️ الرادياتير",
          ),
          QuestionModel(
            question: "أي جزء يُخزن الكهرباء في السيارة؟",
            options: ["🔋 البطارية", "⛽ خزان الوقود", "🔧 المحرك", "❄️ الرادياتير"],
            correctAnswer: "🔋 البطارية",
          ),
          QuestionModel(
            question: "ما هو الجزء الذي يُنقل الحركة للعجلات؟",
            options: ["⚙️ ناقل الحركة", "🔧 المحرك", "🛞 الإطارات", "🦶 الفرامل"],
            correctAnswer: "⚙️ ناقل الحركة",
          ),
          QuestionModel(
            question: "أي جزء يُنظف الزجاج الأمامي؟",
            options: ["🧽 المساحات", "💡 المصابيح", "🚗 المقود", "📯 الكلاكس"],
            correctAnswer: "🧽 المساحات",
          ),
          QuestionModel(
            question: "ما هو الجزء الذي يُخرج العادم؟",
            options: ["🔥 أنبوب العادم", "❄️ الرادياتير", "⛽ خزان الوقود", "🔋 البطارية"],
            correctAnswer: "🔥 أنبوب العادم",
          ),
          QuestionModel(
            question: "أي جزء يُظهر سرعة السيارة؟",
            options: ["📊 عداد السرعة", "⛽ عداد الوقود", "🌡️ مقياس الحرارة", "🔋 مؤشر البطارية"],
            correctAnswer: "📊 عداد السرعة",
          ),
          QuestionModel(
            question: "ما هو الجزء الذي يُحمل الأمتعة؟",
            options: ["🧳 صندوق السيارة", "💺 المقاعد", "🚗 المقود", "🛞 العجلات"],
            correctAnswer: "🧳 صندوق السيارة",
          ),
          QuestionModel(
            question: "أي جزء يُساعد في الرؤية الخلفية؟",
            options: ["🪞 المرايا", "💡 المصابيح", "🪟 النوافذ", "📯 الكلاكس"],
            correctAnswer: "🪞 المرايا",
          ),
          QuestionModel(
            question: "ما هو الجزء الذي يُمتص الصدمات؟",
            options: ["🔧 نظام التعليق", "🛞 الإطارات", "🦶 الفرامل", "⚙️ ناقل الحركة"],
            correctAnswer: "🔧 نظام التعليق",
          ),
          QuestionModel(
            question: "أي جزء يُحافظ على استقرار السيارة؟",
            options: ["⚖️ نظام التوازن", "🚗 المقود", "🛞 الإطارات", "🔧 المحرك"],
            correctAnswer: "⚖️ نظام التوازن",
          ),
        ];

      default:
        return _getAdvancedCarsQuestions(level);
    }
  }

  // الحصول على أسئلة السيارات المتقدمة للمستويات 3-10
  List<QuestionModel> _getAdvancedCarsQuestions(int level) {
    List<List<QuestionModel>> advancedQuestions = [
      // المستوى 3: أنواع السيارات
      [
        QuestionModel(
          question: "ما هو نوع السيارة المخصص للطرق الوعرة؟",
          options: ["🚙 SUV", "🏎️ سيارة رياضية", "🚗 سيدان", "🚐 ميني فان"],
          correctAnswer: "🚙 SUV",
        ),
        QuestionModel(
          question: "أي نوع سيارة مصمم للسرعة العالية؟",
          options: ["🏎️ سيارة رياضية", "🚛 شاحنة", "🚌 حافلة", "🚜 جرار"],
          correctAnswer: "🏎️ سيارة رياضية",
        ),
        QuestionModel(
          question: "ما هو نوع السيارة الأكثر اقتصاداً في الوقود؟",
          options: ["🔋 السيارة الهجينة", "🏎️ السيارة الرياضية", "🚛 الشاحنة", "🚙 SUV"],
          correctAnswer: "🔋 السيارة الهجينة",
        ),
        QuestionModel(
          question: "أي نوع سيارة له سقف قابل للطي؟",
          options: ["🌞 الكابريوليه", "🚗 السيدان", "🚙 SUV", "🚐 الميني فان"],
          correctAnswer: "🌞 الكابريوليه",
        ),
        QuestionModel(
          question: "ما هو نوع السيارة المخصص لنقل البضائع؟",
          options: ["🚛 الشاحنة", "🏎️ السيارة الرياضية", "🌞 الكابريوليه", "🚗 السيدان"],
          correctAnswer: "🚛 الشاحنة",
        ),
        QuestionModel(
          question: "أي نوع سيارة له أربعة أبواب؟",
          options: ["🚗 السيدان", "🏎️ الكوبيه", "🌞 الكابريوليه", "🚜 الجرار"],
          correctAnswer: "🚗 السيدان",
        ),
        QuestionModel(
          question: "ما هو نوع السيارة المخصص للعائلات الكبيرة؟",
          options: ["🚐 الميني فان", "🏎️ السيارة الرياضية", "🌞 الكابريوليه", "🚜 الجرار"],
          correctAnswer: "🚐 الميني فان",
        ),
        QuestionModel(
          question: "أي نوع سيارة يعمل بالكهرباء فقط؟",
          options: ["⚡ السيارة الكهربائية", "⛽ السيارة التقليدية", "🔋 السيارة الهجينة", "🚜 الجرار"],
          correctAnswer: "⚡ السيارة الكهربائية",
        ),
        QuestionModel(
          question: "ما هو نوع السيارة ذات البابين؟",
          options: ["🏎️ الكوبيه", "🚗 السيدان", "🚐 الميني فان", "🚛 الشاحنة"],
          correctAnswer: "🏎️ الكوبيه",
        ),
        QuestionModel(
          question: "أي نوع سيارة مخصص للأعمال الزراعية؟",
          options: ["🚜 الجرار", "🏎️ السيارة الرياضية", "🚗 السيدان", "🌞 الكابريوليه"],
          correctAnswer: "🚜 الجرار",
        ),
      ],
    ];

    if (level == 3) {
      return advancedQuestions[0];
    }

    // للمستويات 4-10: أسئلة متقدمة عن السيارات
    return _getExpertCarsQuestions(level);
  }

  // الحصول على أسئلة السيارات الخبيرة للمستويات 4-10
  List<QuestionModel> _getExpertCarsQuestions(int level) {
    List<List<QuestionModel>> expertQuestions = [
      // المستوى 4: ماركات السيارات الشهيرة
      [
        QuestionModel(
          question: "ما هي الشركة المصنعة لسيارة كورولا؟",
          options: ["🚗 تويوتا", "🚗 هوندا", "🚗 نيسان", "🚗 مازدا"],
          correctAnswer: "🚗 تويوتا",
        ),
        QuestionModel(
          question: "أي شركة تصنع سيارة BMW؟",
          options: ["🇩🇪 ألمانيا", "🇯🇵 اليابان", "🇺🇸 أمريكا", "🇮🇹 إيطاليا"],
          correctAnswer: "🇩🇪 ألمانيا",
        ),
        QuestionModel(
          question: "ما هي الشركة المصنعة لسيارة فيراري؟",
          options: ["🇮🇹 إيطاليا", "🇩🇪 ألمانيا", "🇫🇷 فرنسا", "🇬🇧 بريطانيا"],
          correctAnswer: "🇮🇹 إيطاليا",
        ),
        QuestionModel(
          question: "أي شركة تشتهر بشعار الحصان الجامح؟",
          options: ["🐎 فيراري", "🦁 بيجو", "🐆 جاكوار", "🦌 هارتلي"],
          correctAnswer: "🐎 فيراري",
        ),
        QuestionModel(
          question: "ما هي الشركة المصنعة لسيارة مرسيدس؟",
          options: ["🇩🇪 ألمانيا", "🇯🇵 اليابان", "🇺🇸 أمريكا", "🇮🇹 إيطاليا"],
          correctAnswer: "🇩🇪 ألمانيا",
        ),
        QuestionModel(
          question: "أي شركة تصنع سيارة لكزس؟",
          options: ["🚗 تويوتا", "🚗 هوندا", "🚗 نيسان", "🚗 مازدا"],
          correctAnswer: "🚗 تويوتا",
        ),
        QuestionModel(
          question: "ما هي الشركة المصنعة لسيارة أودي؟",
          options: ["🇩🇪 ألمانيا", "🇯🇵 اليابان", "🇺🇸 أمريكا", "🇮🇹 إيطاليا"],
          correctAnswer: "🇩🇪 ألمانيا",
        ),
        QuestionModel(
          question: "أي شركة تشتهر بشعار الدوائر الأربع؟",
          options: ["⭕ أودي", "🔵 BMW", "⭐ مرسيدس", "🔶 فولكس فاجن"],
          correctAnswer: "⭕ أودي",
        ),
        QuestionModel(
          question: "ما هي الشركة المصنعة لسيارة كامري؟",
          options: ["🚗 تويوتا", "🚗 هوندا", "🚗 نيسان", "🚗 مازدا"],
          correctAnswer: "🚗 تويوتا",
        ),
        QuestionModel(
          question: "أي شركة تصنع سيارة رولز رويس؟",
          options: ["🇬🇧 بريطانيا", "🇩🇪 ألمانيا", "🇺🇸 أمريكا", "🇮🇹 إيطاليا"],
          correctAnswer: "🇬🇧 بريطانيا",
        ),
      ],

      // المستوى 5: تقنيات السيارات
      [
        QuestionModel(
          question: "ما هو نظام ABS في السيارة؟",
          options: ["🛑 نظام منع انغلاق الفرامل", "🔧 نظام تشغيل المحرك", "❄️ نظام التبريد", "⚙️ نظام ناقل الحركة"],
          correctAnswer: "🛑 نظام منع انغلاق الفرامل",
        ),
        QuestionModel(
          question: "أي نظام يساعد في الحفاظ على الاتجاه؟",
          options: ["🎯 نظام التحكم في الثبات", "🔧 نظام الإشعال", "❄️ نظام التبريد", "⛽ نظام الوقود"],
          correctAnswer: "🎯 نظام التحكم في الثبات",
        ),
        QuestionModel(
          question: "ما هو GPS في السيارة؟",
          options: ["🗺️ نظام تحديد المواقع", "📻 نظام الراديو", "❄️ نظام التبريد", "🔧 نظام المحرك"],
          correctAnswer: "🗺️ نظام تحديد المواقع",
        ),
        QuestionModel(
          question: "أي نظام يقلل من انبعاثات العادم؟",
          options: ["🌱 المحول الحفزي", "🔧 المحرك", "⛽ خزان الوقود", "❄️ الرادياتير"],
          correctAnswer: "🌱 المحول الحفزي",
        ),
        QuestionModel(
          question: "ما هو نظام الوسائد الهوائية؟",
          options: ["🛡️ نظام الأمان", "🔧 نظام المحرك", "❄️ نظام التبريد", "⚙️ نظام ناقل الحركة"],
          correctAnswer: "🛡️ نظام الأمان",
        ),
        QuestionModel(
          question: "أي نظام يساعد في صف السيارة؟",
          options: ["🅿️ نظام المساعدة في الركن", "🔧 نظام المحرك", "❄️ نظام التبريد", "⛽ نظام الوقود"],
          correctAnswer: "🅿️ نظام المساعدة في الركن",
        ),
        QuestionModel(
          question: "ما هو نظام التحكم في السرعة؟",
          options: ["🎯 نظام التحكم التلقائي", "🔧 نظام المحرك", "❄️ نظام التبريد", "⚙️ نظام ناقل الحركة"],
          correctAnswer: "🎯 نظام التحكم التلقائي",
        ),
        QuestionModel(
          question: "أي نظام يراقب ضغط الإطارات؟",
          options: ["📊 نظام مراقبة ضغط الإطارات", "🔧 نظام المحرك", "❄️ نظام التبريد", "⛽ نظام الوقود"],
          correctAnswer: "📊 نظام مراقبة ضغط الإطارات",
        ),
        QuestionModel(
          question: "ما هو نظام البلوتوث في السيارة؟",
          options: ["📱 نظام الاتصال اللاسلكي", "🔧 نظام المحرك", "❄️ نظام التبريد", "⚙️ نظام ناقل الحركة"],
          correctAnswer: "📱 نظام الاتصال اللاسلكي",
        ),
        QuestionModel(
          question: "أي نظام يحذر من الخروج عن المسار؟",
          options: ["⚠️ نظام تحذير مغادرة المسار", "🔧 نظام المحرك", "❄️ نظام التبريد", "⛽ نظام الوقود"],
          correctAnswer: "⚠️ نظام تحذير مغادرة المسار",
        ),
      ],
    ];

    if (level >= 4 && level <= 5) {
      return expertQuestions[level - 4];
    }

    // للمستويات 6-10: أسئلة متقدمة جداً
    return _getSuperAdvancedCarsQuestions(level);
  }

  // الحصول على أسئلة السيارات المتقدمة جداً للمستويات 6-10
  List<QuestionModel> _getSuperAdvancedCarsQuestions(int level) {
    // أسئلة افتراضية للمستويات المتقدمة
    return [
      QuestionModel(
        question: "ما هو أسرع سيارة في العالم؟",
        options: ["🏎️ بوجاتي شيرون", "🏎️ كونيجسيج", "🏎️ مكلارين", "🏎️ فيراري"],
        correctAnswer: "🏎️ كونيجسيج",
      ),
      QuestionModel(
        question: "أي سيارة تعتبر الأغلى في العالم؟",
        options: ["💎 رولز رويس", "💎 بوجاتي", "💎 لامبورجيني", "💎 فيراري"],
        correctAnswer: "💎 بوجاتي",
      ),
      QuestionModel(
        question: "ما هو أول محرك احتراق داخلي؟",
        options: ["🔧 محرك أوتو", "🔧 محرك ديزل", "🔧 محرك بخاري", "🔧 محرك كهربائي"],
        correctAnswer: "🔧 محرك أوتو",
      ),
      QuestionModel(
        question: "أي شركة اخترعت الوسادة الهوائية؟",
        options: ["🛡️ مرسيدس", "🛡️ BMW", "🛡️ أودي", "🛡️ فولفو"],
        correctAnswer: "🛡️ مرسيدس",
      ),
      QuestionModel(
        question: "ما هو أكبر محرك سيارة؟",
        options: ["🔧 W16", "🔧 V12", "🔧 V8", "🔧 V6"],
        correctAnswer: "🔧 W16",
      ),
      QuestionModel(
        question: "أي سيارة لها أكبر استهلاك وقود؟",
        options: ["🚛 الشاحنات الثقيلة", "🏎️ السيارات الرياضية", "🚙 SUV", "🚗 السيدان"],
        correctAnswer: "🚛 الشاحنات الثقيلة",
      ),
      QuestionModel(
        question: "ما هو أول نظام ملاحة في السيارات؟",
        options: ["🗺️ GPS", "📻 الراديو", "📱 الهاتف", "🧭 البوصلة"],
        correctAnswer: "🗺️ GPS",
      ),
      QuestionModel(
        question: "أي شركة رائدة في السيارات الكهربائية؟",
        options: ["⚡ تسلا", "⚡ نيسان", "⚡ BMW", "⚡ تويوتا"],
        correctAnswer: "⚡ تسلا",
      ),
      QuestionModel(
        question: "ما هو أطول عمر لسيارة؟",
        options: ["📅 أكثر من 50 سنة", "📅 أكثر من 30 سنة", "📅 أكثر من 20 سنة", "📅 أكثر من 10 سنوات"],
        correctAnswer: "📅 أكثر من 50 سنة",
      ),
      QuestionModel(
        question: "أي تقنية تُستخدم في السيارات ذاتية القيادة؟",
        options: ["🤖 الذكاء الاصطناعي", "📻 الراديو", "❄️ التبريد", "⛽ الوقود"],
        correctAnswer: "🤖 الذكاء الاصطناعي",
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show Category Select Screen
    return CategorySelectScreen(
      categories: categories,
      audioService: audioService,
      onCategorySelected: (categoryInfo) {
        List<LevelModel> levelsToShow = [];
        Function(int, int, int) progressUpdater = (_, __, ___) {}; // Default empty function

        switch (categoryInfo.category) {
          case QuizCategory.islamic:
            levelsToShow = islamicLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.islamic, index, stars, score);
            break;
          case QuizCategory.math:
            levelsToShow = mathLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.math, index, stars, score);
            break;
          case QuizCategory.historical:
            levelsToShow = historicalLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.historical, index, stars, score);
            break;
          case QuizCategory.videoGames:
            levelsToShow = videoGamesLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.videoGames, index, stars, score);
            break;
          case QuizCategory.sports:
            levelsToShow = sportsLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.sports, index, stars, score);
            break;
          case QuizCategory.literature:
            levelsToShow = literatureLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.literature, index, stars, score);
            break;
          case QuizCategory.animals:
            levelsToShow = animalsLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.animals, index, stars, score);
            break;
          case QuizCategory.plants:
            levelsToShow = plantsLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.plants, index, stars, score);
            break;
          case QuizCategory.cars:
            levelsToShow = carsLevels;
            progressUpdater = (index, stars, score) => _updateLevelProgress(QuizCategory.cars, index, stars, score);
            break;
        }

        if (levelsToShow.isNotEmpty) {
           Navigator.push(
             context,
             MaterialPageRoute(
               builder: (context) => LevelSelectScreen(
                 categoryTitle: categoryInfo.title, // Pass category title
                 levels: levelsToShow,
                 audioService: audioService,
                 onLevelComplete: progressUpdater,
                 backgroundGradient: categoryInfo.gradient, // Pass gradient
                 category: categoryInfo.category, // Pass category
               ),
             ),
           );
        } else {
           // Optionally handle the case where levels might be empty (e.g., show a message)
           debugPrint("No levels found for category: ${categoryInfo.category}");
        }
      },
    );
  }
}

// --- Category Select Screen ---
class CategorySelectScreen extends StatelessWidget {
  final List<CategoryInfo> categories;
  final AudioService audioService;
  final Function(CategoryInfo) onCategorySelected;

  const CategorySelectScreen({
    super.key,
    required this.categories,
    required this.audioService,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600; // تحديد ما إذا كان الجهاز تابلت
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Stack(
          children: [
            // خلفية متدرجة محسنة مع تأثيرات إبداعية متطورة
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF0F0C29), // Deep cosmic purple
                    Color(0xFF24243e), // Dark space blue
                    Color(0xFF302B63), // Royal purple
                    Color(0xFF0F4C75), // Deep ocean blue
                    Color(0xFF3282B8), // Bright blue
                    Color(0xFF0F3460), // Navy blue
                  ],
                  stops: [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
                ),
              ),
            ),
            // طبقة تدرج إضافية للعمق والجمال المتطور
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [
                    Colors.purple.withOpacity(0.4),
                    Colors.transparent,
                    Colors.cyan.withOpacity(0.3),
                    Colors.transparent,
                    Colors.pink.withOpacity(0.2),
                    Colors.transparent,
                    Colors.amber.withOpacity(0.1),
                  ],
                ),
              ),
            ),
            // طبقة تأثيرات إضافية للجمال
            Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.center,
                  radius: 2.0,
                  colors: [
                    Colors.white.withOpacity(0.1),
                    Colors.transparent,
                    Colors.blue.withOpacity(0.05),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
            
            // نمط زخرفي للخلفية
            Positioned.fill(
              child: Opacity(
                opacity: 0.05,
                child: Image.asset(
                  'assets/images/pattern.png',
                  repeat: ImageRepeat.repeat,
                  errorBuilder: (context, error, stackTrace) => const SizedBox(), // تجنب الخطأ إذا لم يوجد الملف
                ),
              ),
            ),

            // محتوى الصفحة
            SafeArea(
              child: Column(
                children: [
                  // ترويسة التطبيق
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                    child: Column(
                      children: [
                        // شعار التطبيق محسن
                        Hero(
                          tag: 'app_logo',
                          child: TweenAnimationBuilder<double>(
                            duration: const Duration(seconds: 3),
                            tween: Tween(begin: 0.0, end: 1.0),

                            builder: (context, value, child) {
                              return Transform.scale(
                                scale: 1.0 + (math.sin(value * math.pi * 2) * 0.05),
                                child: Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    gradient: RadialGradient(
                                      colors: [
                                        Colors.white,
                                        Colors.white.withOpacity(0.95),
                                        Colors.white.withOpacity(0.85),
                                      ],
                                      stops: const [0.0, 0.7, 1.0],
                                    ),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.8),
                                      width: 3,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.3),
                                        spreadRadius: 3,
                                        blurRadius: 20,
                                        offset: const Offset(0, 8),
                                      ),
                                      BoxShadow(
                                        color: Colors.white.withOpacity(0.5),
                                        spreadRadius: 8,
                                        blurRadius: 30,
                                        offset: const Offset(0, 0),
                                      ),
                                    ],
                                  ),
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      // توهج خلفي
                                      Container(
                                        width: 80,
                                        height: 80,
                                        decoration: BoxDecoration(
                                          gradient: RadialGradient(
                                            colors: [
                                              const Color(0xFF3282B8).withOpacity(0.3),
                                              Colors.transparent,
                                            ],
                                          ),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      // الأيقونة الرئيسية
                                      Icon(
                                        Icons.psychology,
                                        size: 70,
                                        color: const Color(0xFF0F4C75),
                                        shadows: [
                                          Shadow(
                                            color: Colors.black.withOpacity(0.3),
                                            blurRadius: 8,
                                            offset: const Offset(0, 4),
                                          ),
                                        ],
                                      ),
                                      // نجوم صغيرة متحركة
                                      ...List.generate(6, (index) =>
                                        Positioned(
                                          left: 20 + (index * 15.0),
                                          top: 15 + (math.sin(index + value * math.pi * 2) * 10),
                                          child: Icon(
                                            Icons.star,
                                            size: 8,
                                            color: Colors.amber.withOpacity(0.7),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 20),
                        // اسم التطبيق محسن
                        ShaderMask(
                          shaderCallback: (bounds) => const LinearGradient(
                            colors: [
                              Colors.white,
                              Color(0xFFE3F2FD),
                              Colors.white,
                              Color(0xFFBBDEFB),
                            ],
                            stops: [0.0, 0.3, 0.7, 1.0],
                          ).createShader(bounds),
                          child: const Text(
                            "المسابقات التعليمية",
                            style: TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.w900,
                              color: Colors.white,
                              letterSpacing: 1.5,
                              shadows: [
                                Shadow(
                                  color: Colors.black54,
                                  offset: Offset(0, 4),
                                  blurRadius: 8,
                                ),
                                Shadow(
                                  color: Colors.blue,
                                  offset: Offset(0, 0),
                                  blurRadius: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 5),
                        // شعار التطبيق محسن
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.amber.withOpacity(0.4),
                                Colors.orange.withOpacity(0.3),
                                Colors.amber.withOpacity(0.2),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.amber.withOpacity(0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.emoji_events,
                                color: Colors.amber.shade300,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                "اختبر معلوماتك وتحدى نفسك!",
                                style: TextStyle(
                                  fontSize: 17,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                  letterSpacing: 0.5,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black45,
                                      offset: Offset(0, 2),
                                      blurRadius: 4,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 8),
                              Icon(
                                Icons.psychology,
                                color: Colors.blue.shade300,
                                size: 20,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // شبكة الفئات - تصميم مدمج بدون تمرير
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: GridView.count(
                      shrinkWrap: true, // تقليص الحجم حسب المحتوى
                      physics: const NeverScrollableScrollPhysics(), // منع التمرير
                      crossAxisCount: 3, // 3 بطاقات في الصف
                      childAspectRatio: 0.8, // نسبة مناسبة للبطاقات الصغيرة
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      children: List.generate(categories.length, (index) {
                        return _buildCompactCategoryTile(context, categories[index], index);
                      }),
                    ),
                  ),

                  // تذييل التطبيق
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Text(
                      "تطبيق المسابقات التعليمية - ${DateTime.now().year} ©",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // زخارف متحركة عبقرية
            Positioned(
              top: -30,
              left: -30,
              child: TweenAnimationBuilder<double>(
                duration: const Duration(seconds: 4),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.rotate(
                    angle: -0.2 + (value * 0.1),
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [
                            Colors.white.withOpacity(0.15),
                            Colors.white.withOpacity(0.05),
                            Colors.transparent,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withOpacity(0.1),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            Positioned(
              bottom: -20,
              right: -20,
              child: TweenAnimationBuilder<double>(
                duration: const Duration(seconds: 5),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.rotate(
                    angle: 0.3 + (value * -0.15),
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [
                            Colors.white.withOpacity(0.12),
                            Colors.white.withOpacity(0.04),
                            Colors.transparent,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withOpacity(0.08),
                            blurRadius: 25,
                            spreadRadius: 8,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            // جسيمات عائمة إضافية
            Positioned(
              top: 100,
              right: 50,
              child: TweenAnimationBuilder<double>(
                duration: const Duration(seconds: 6),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.translate(
                    offset: Offset(0, math.sin(value * math.pi * 2) * 10),
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [
                            Colors.amber.withOpacity(0.2),
                            Colors.transparent,
                          ],
                        ),
                        shape: BoxShape.circle,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryTile(BuildContext context, CategoryInfo categoryInfo, int index) {
    // قائمة من الرموز التعبيرية المناسبة لكل فئة
    final List<String> emojis = [
      '🕌', // إسلامي
      '🧮', // رياضيات
      '🏛️', // تاريخ
      '🎮', // ألعاب فيديو
      '⚽', // رياضة
      '📖', // أدب وشعر
      '🦁', // حيوانات - أسد بدلاً من كتاب
      '🌺', // نباتات - زهرة بدلاً من كتاب
      '🚗', // سيارات - سيارة بدلاً من كتاب
    ];

    // رمز تعبيري مناسب للفئة
    final emoji = index < emojis.length ? emojis[index] : '📚';

    return Hero(
      tag: 'category_card_${categoryInfo.title}',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            audioService.playClickSound();
            onCategorySelected(categoryInfo);
          },
          borderRadius: BorderRadius.circular(24),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  categoryInfo.gradient[0],
                  categoryInfo.gradient[1],
                  categoryInfo.gradient[0].withOpacity(0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: categoryInfo.gradient[0].withOpacity(0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 1,
                  offset: const Offset(0, 1),
                ),
              ],
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1.5,
              ),
            ),
            child: Stack(
              children: [
                // تأثير لمعان في الخلفية
                Positioned(
                  top: -20,
                  right: -20,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          Colors.white.withOpacity(0.1),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ),
                // المحتوى الرئيسي
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // دائرة تحتوي على أيقونة الفئة محسنة
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.white.withOpacity(0.3),
                              Colors.white.withOpacity(0.1),
                            ],
                          ),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white.withOpacity(0.4),
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Text(
                              emoji,
                              style: const TextStyle(fontSize: 36),
                            ),
                            Positioned(
                              bottom: 8,
                              right: 8,
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.9),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  categoryInfo.icon,
                                  size: 16,
                                  color: categoryInfo.gradient[0],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // عنوان الفئة محسن
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: Text(
                          categoryInfo.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                blurRadius: 2,
                                offset: Offset(0, 1),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // شارة عدد المستويات محسنة
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white.withOpacity(0.4),
                              Colors.white.withOpacity(0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.layers,
                              size: 14,
                              color: Colors.white.withOpacity(0.9),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              "10 مستويات",
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء بطاقة فئة مدمجة وصغيرة مع تأثيرات عبقرية
  Widget _buildCompactCategoryTile(BuildContext context, CategoryInfo categoryInfo, int index) {
    // قائمة من الرموز التعبيرية المناسبة لكل فئة
    final List<String> emojis = [
      '🕌', // إسلامي
      '🧮', // رياضيات
      '🏛️', // تاريخ
      '🎮', // ألعاب فيديو
      '⚽', // رياضة
      '📖', // أدب وشعر
      '🦁', // حيوانات - أسد بدلاً من كتاب
      '🌺', // نباتات - زهرة بدلاً من كتاب
      '🚗', // سيارات - سيارة بدلاً من كتاب
    ];

    // رمز تعبيري مناسب للفئة
    final emoji = index < emojis.length ? emojis[index] : '📚';

    return Hero(
      tag: 'compact_category_card_${categoryInfo.title}',
      child: Material(
        color: Colors.transparent,
        child: TweenAnimationBuilder<double>(
          duration: Duration(milliseconds: 800 + (index * 200)),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, animationValue, child) {
            return Transform.scale(
              scale: 0.8 + (animationValue * 0.2),
              child: Opacity(
                opacity: animationValue,
                child: InkWell(
                  onTap: () {
                    audioService.playClickSound();
                    onCategorySelected(categoryInfo);
                  },
                  borderRadius: BorderRadius.circular(20),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: categoryInfo.gradient.length >= 4
                          ? [
                              categoryInfo.gradient[0],
                              categoryInfo.gradient[1],
                              categoryInfo.gradient[2],
                              categoryInfo.gradient[3],
                            ]
                          : [
                              categoryInfo.gradient[0],
                              categoryInfo.gradient[1],
                              categoryInfo.gradient.length > 2 ? categoryInfo.gradient[2] : categoryInfo.gradient[1],
                            ],
                        stops: categoryInfo.gradient.length >= 4
                          ? [0.0, 0.3, 0.7, 1.0]
                          : [0.0, 0.5, 1.0],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        // ظل رئيسي عميق
                        BoxShadow(
                          color: categoryInfo.gradient[0].withOpacity(0.6),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                          spreadRadius: 2,
                        ),
                        // ظل ثانوي للعمق
                        BoxShadow(
                          color: categoryInfo.gradient.length > 1
                            ? categoryInfo.gradient[1].withOpacity(0.4)
                            : categoryInfo.gradient[0].withOpacity(0.4),
                          blurRadius: 30,
                          offset: const Offset(0, 15),
                          spreadRadius: 5,
                        ),
                        // ظل داخلي للإضاءة
                        BoxShadow(
                          color: Colors.white.withOpacity(0.15),
                          blurRadius: 2,
                          offset: const Offset(-1, -1),
                          spreadRadius: 0,
                        ),
                      ],
                      border: Border.all(
                        color: Colors.white.withOpacity(0.4),
                        width: 2,
                      ),
                    ),
                    child: Stack(
                      children: [
                        // تأثير لمعان متحرك محسن
                        Positioned(
                          top: -30,
                          right: -30,
                          child: TweenAnimationBuilder<double>(
                            duration: const Duration(seconds: 4),
                            tween: Tween(begin: 0.0, end: 1.0),

                            builder: (context, value, child) {
                              return Transform.rotate(
                                angle: value * math.pi * 2,
                                child: Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    gradient: RadialGradient(
                                      colors: [
                                        Colors.white.withOpacity(0.3),
                                        categoryInfo.gradient[0].withOpacity(0.2),
                                        Colors.transparent,
                                      ],
                                      stops: const [0.0, 0.5, 1.0],
                                    ),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),

                        // تأثير لمعان ثاني من الجهة المقابلة
                        Positioned(
                          bottom: -25,
                          left: -25,
                          child: TweenAnimationBuilder<double>(
                            duration: const Duration(seconds: 5),
                            tween: Tween(begin: 0.0, end: 1.0),

                            builder: (context, value, child) {
                              return Transform.rotate(
                                angle: -value * math.pi * 1.5,
                                child: Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    gradient: RadialGradient(
                                      colors: [
                                        categoryInfo.gradient.length > 1
                                          ? categoryInfo.gradient[1].withOpacity(0.25)
                                          : categoryInfo.gradient[0].withOpacity(0.25),
                                        Colors.white.withOpacity(0.1),
                                        Colors.transparent,
                                      ],
                                      stops: const [0.0, 0.6, 1.0],
                                    ),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        // المحتوى الرئيسي
                        Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // دائرة أيقونة محسنة بتصميم عصري
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  gradient: RadialGradient(
                                    colors: [
                                      Colors.white.withOpacity(0.4),
                                      Colors.white.withOpacity(0.2),
                                      Colors.white.withOpacity(0.05),
                                    ],
                                    stops: const [0.0, 0.7, 1.0],
                                  ),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.6),
                                    width: 3,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                    BoxShadow(
                                      color: Colors.white.withOpacity(0.3),
                                      blurRadius: 15,
                                      offset: const Offset(0, 0),
                                      spreadRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    TweenAnimationBuilder<double>(
                                      duration: const Duration(seconds: 3),
                                      tween: Tween(begin: 0.0, end: 1.0),

                                      builder: (context, value, child) {
                                        return Transform.scale(
                                          scale: 1.0 + (math.sin(value * math.pi * 2) * 0.15),
                                          child: Transform.rotate(
                                            angle: math.sin(value * math.pi * 2) * 0.1,
                                            child: Text(
                                              emoji,
                                              style: TextStyle(
                                                fontSize: 28,
                                                shadows: [
                                                  Shadow(
                                                    color: Colors.black.withOpacity(0.3),
                                                    blurRadius: 4,
                                                    offset: const Offset(0, 2),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                    Positioned(
                                      bottom: 0,
                                      right: 0,
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              categoryInfo.gradient[0],
                                              categoryInfo.gradient.length > 1
                                                ? categoryInfo.gradient[1]
                                                : categoryInfo.gradient[0].withOpacity(0.7),
                                            ],
                                          ),
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.white.withOpacity(0.8),
                                            width: 2,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.3),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: Icon(
                                          categoryInfo.icon,
                                          size: 12,
                                          color: Colors.white,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black.withOpacity(0.5),
                                              blurRadius: 2,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              // عنوان محسن بتأثيرات متقدمة
                              Text(
                                categoryInfo.title,
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w800,
                                  color: Colors.white,
                                  letterSpacing: 0.5,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black.withOpacity(0.6),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                    Shadow(
                                      color: categoryInfo.gradient[0].withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 0),
                                    ),
                                  ],
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 6),
                              // شارة محسنة بتصميم عصري
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      categoryInfo.gradient[0].withOpacity(0.8),
                                      categoryInfo.gradient.length > 1
                                        ? categoryInfo.gradient[1].withOpacity(0.6)
                                        : categoryInfo.gradient[0].withOpacity(0.6),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.5),
                                    width: 1.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.star,
                                      size: 10,
                                      color: Colors.amber.shade300,
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      "10",
                                      style: TextStyle(
                                        fontSize: 11,
                                        fontWeight: FontWeight.w900,
                                        color: Colors.white,
                                        shadows: [
                                          Shadow(
                                            color: Colors.black.withOpacity(0.5),
                                            blurRadius: 2,
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

// --- Category Card ---
class CategoryCard extends StatefulWidget {
  final CategoryInfo categoryInfo;
  final VoidCallback onTap;

  const CategoryCard({
    super.key,
    required this.categoryInfo,
    required this.onTap,
  });

  @override
  State<CategoryCard> createState() => _CategoryCardState();
}

class _CategoryCardState extends State<CategoryCard>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _glowController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    // تأثيرات حركية خاصة لبطاقة الحيوانات
    if (widget.categoryInfo.category == QuizCategory.animals) {
      _pulseController = AnimationController(
        duration: const Duration(seconds: 2),
        vsync: this,
      );
      _glowController = AnimationController(
        duration: const Duration(seconds: 3),
        vsync: this,
      );

      _pulseAnimation = Tween<double>(
        begin: 1.0,
        end: 1.08, // زيادة التأثير ليكون أكثر وضوحاً
      ).animate(CurvedAnimation(
        parent: _pulseController,
        curve: Curves.easeInOut,
      ));

      _glowAnimation = Tween<double>(
        begin: 0.4,
        end: 1.0, // زيادة التوهج ليكون أكثر وضوحاً
      ).animate(CurvedAnimation(
        parent: _glowController,
        curve: Curves.easeInOut,
      ));

      // بدء التأثيرات
      _pulseController.repeat(reverse: true);
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    if (widget.categoryInfo.category == QuizCategory.animals) {
      _pulseController.dispose();
      _glowController.dispose();
    }
    super.dispose();
  }

  // بناء محتوى البطاقة
  Widget _buildCardContent() {
    return Ink(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.categoryInfo.category == QuizCategory.animals
            ? [
                // تدرج خاص مذهل لعالم الحيوانات - برتقالي وبني
                const Color(0xFF7C2D12), // بني داكن - عمق الأرض البرية
                const Color(0xFF9A3412), // بني متوسط - قوة الحيوانات
                const Color(0xFFEA580C), // برتقالي داكن - حيوية الحياة البرية
                const Color(0xFFF97316), // برتقالي - طاقة الحيوانات
                const Color(0xFFFB923C), // برتقالي فاتح - دفء الطبيعة
                const Color(0xFFFDBA74), // برتقالي ناعم - إشراق الحياة
              ]
            : widget.categoryInfo.category == QuizCategory.plants
              ? [
                  // تدرج خاص مميز لعالم النباتات
                  const Color(0xFF1B5E20), // أخضر زمردي داكن
                  const Color(0xFF2E7D32), // أخضر غابات عميق
                  const Color(0xFF43A047), // أخضر غني
                  const Color(0xFF66BB6A), // أخضر منعش
                  const Color(0xFF81C784), // أخضر فاتح منعش
                  const Color(0xFF4CAF50), // أخضر نابض بالحياة
                  const Color(0xFF8BC34A), // أخضر ليموني
                  const Color(0xFF9CCC65), // أخضر ليموني فاتح
                ]
              : widget.categoryInfo.gradient,
          begin: widget.categoryInfo.category == QuizCategory.plants
            ? Alignment.topCenter // اتجاه مختلف للنباتات
            : Alignment.topLeft,
          end: widget.categoryInfo.category == QuizCategory.plants
            ? Alignment.bottomCenter // اتجاه مختلف للنباتات
            : Alignment.bottomRight,
          stops: widget.categoryInfo.category == QuizCategory.animals
            ? [0.0, 0.2, 0.4, 0.6, 0.8, 1.0] // توزيع متدرج للألوان
            : widget.categoryInfo.category == QuizCategory.plants
              ? [0.0, 0.15, 0.3, 0.45, 0.6, 0.75, 0.9, 1.0] // توزيع مختلف للنباتات
              : null,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: widget.categoryInfo.category == QuizCategory.animals
          ? [
              // ظلال متعددة الطبقات لبطاقة الحيوانات - برتقالية
              BoxShadow(
                color: const Color(0xFF9A3412).withOpacity(0.5),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 3,
              ),
              BoxShadow(
                color: const Color(0xFFF97316).withOpacity(0.3),
                blurRadius: 30,
                offset: const Offset(0, 15),
                spreadRadius: 5,
              ),
              // ظل داخلي للعمق
              BoxShadow(
                color: Colors.white.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(-3, -3),
                spreadRadius: 1,
              ),
              // ظل خارجي ناعم برتقالي
              BoxShadow(
                color: const Color(0xFFFB923C).withOpacity(0.2),
                blurRadius: 40,
                offset: const Offset(0, 20),
                spreadRadius: 8,
              ),
            ]
          : widget.categoryInfo.category == QuizCategory.plants
            ? [
                // ظلال خاصة لبطاقة النباتات - تأثير أوراق الشجر
                BoxShadow(
                  color: const Color(0xFF43A047).withOpacity(0.6),
                  blurRadius: 25,
                  offset: const Offset(0, 12),
                  spreadRadius: 4,
                ),
                BoxShadow(
                  color: const Color(0xFF8BC34A).withOpacity(0.4),
                  blurRadius: 35,
                  offset: const Offset(0, 18),
                  spreadRadius: 6,
                ),
                // ظل ليموني ناعم
                BoxShadow(
                  color: const Color(0xFF9CCC65).withOpacity(0.3),
                  blurRadius: 45,
                  offset: const Offset(0, 25),
                  spreadRadius: 10,
                ),
                // ظل داخلي للعمق
                BoxShadow(
                  color: Colors.white.withOpacity(0.15),
                  blurRadius: 10,
                  offset: const Offset(-2, -2),
                  spreadRadius: 2,
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5)
                )
              ],
        // إضافة حدود متوهجة للبطاقات الخاصة
        border: widget.categoryInfo.category == QuizCategory.animals
          ? Border.all(
              color: Colors.white.withOpacity(0.6), // زيادة الوضوح
              width: 3, // زيادة السماكة
            )
          : widget.categoryInfo.category == QuizCategory.plants
            ? Border.all(
                color: const Color(0xFF8BC34A).withOpacity(0.7), // حدود ليمونية للنباتات
                width: 3,
              )
            : null,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
        child: Row(
          children: [
            // تحسين أيقونة الحيوانات والنباتات
            widget.categoryInfo.category == QuizCategory.animals
              ? Stack(
                  alignment: Alignment.center,
                  children: [
                    // دائرة متوهجة خلف الأيقونة
                    Container(
                      width: 80, // زيادة الحجم
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            Colors.white.withOpacity(0.5), // زيادة الوضوح
                            Colors.white.withOpacity(0.2),
                            Colors.transparent,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withOpacity(0.7), // زيادة التوهج
                            blurRadius: 25, // زيادة التمويه
                            spreadRadius: 8, // زيادة الانتشار
                          ),
                          // إضافة ظل ثاني للمزيد من التأثير
                          BoxShadow(
                            color: const Color(0xFF4CAF50).withOpacity(0.4),
                            blurRadius: 30,
                            spreadRadius: 10,
                          ),
                        ],
                      ),
                    ),
                    // الأيقونة مع تأثيرات
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withOpacity(0.2),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.4),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        widget.categoryInfo.icon,
                        size: 35,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 5,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                    // إيموجي أرنب على طرف الدائرة
                    Positioned(
                      top: 2,
                      right: 2,
                      child: Text(
                        "🐰",
                        style: TextStyle(
                          fontSize: 14,
                          shadows: [
                            Shadow(
                              color: Colors.white.withOpacity(0.8),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              : widget.categoryInfo.category == QuizCategory.plants
                ? Stack(
                    alignment: Alignment.center,
                    children: [
                      // دائرة متوهجة خاصة بالنباتات - تأثير أوراق الشجر
                      Container(
                        width: 85, // حجم أكبر قليلاً للنباتات
                        height: 85,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              const Color(0xFF8BC34A).withOpacity(0.6), // أخضر ليموني
                              const Color(0xFF9CCC65).withOpacity(0.3), // أخضر ليموني فاتح
                              Colors.transparent,
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF8BC34A).withOpacity(0.8), // توهج ليموني
                              blurRadius: 30,
                              spreadRadius: 10,
                            ),
                            // ظل ثاني بلون مختلف
                            BoxShadow(
                              color: const Color(0xFF9CCC65).withOpacity(0.5),
                              blurRadius: 40,
                              spreadRadius: 15,
                            ),
                          ],
                        ),
                      ),
                      // الأيقونة مع تأثيرات خاصة بالنباتات
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: const Color(0xFF8BC34A).withOpacity(0.3), // خلفية ليمونية
                          border: Border.all(
                            color: const Color(0xFF9CCC65).withOpacity(0.6), // حدود ليمونية
                            width: 3,
                          ),
                        ),
                        child: Icon(
                          widget.categoryInfo.icon,
                          size: 38, // حجم أكبر قليلاً
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: const Color(0xFF1B5E20).withOpacity(0.5), // ظل أخضر داكن
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                      ),
                      // إيموجي نباتات متعددة
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Text(
                          "🌿",
                          style: TextStyle(
                            fontSize: 14,
                            shadows: [
                              Shadow(
                                color: Colors.white.withOpacity(0.9),
                                blurRadius: 3,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        child: Text(
                          "🌱",
                          style: TextStyle(
                            fontSize: 14,
                            shadows: [
                              Shadow(
                                color: Colors.white.withOpacity(0.9),
                                blurRadius: 3,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  )
                : Icon(
                    widget.categoryInfo.icon,
                    size: 50,
                    color: Colors.white.withOpacity(0.9)
                  ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.categoryInfo.title,
                    style: TextStyle(
                      fontSize: widget.categoryInfo.category == QuizCategory.animals
                        ? 22
                        : widget.categoryInfo.category == QuizCategory.plants
                          ? 23 // حجم مميز للنباتات
                          : 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: widget.categoryInfo.category == QuizCategory.animals
                        ? [
                            Shadow(
                              color: Colors.black.withOpacity(0.5),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                            Shadow(
                              color: const Color(0xFF7C2D12).withOpacity(0.8),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ]
                        : widget.categoryInfo.category == QuizCategory.plants
                          ? [
                              Shadow(
                                color: Colors.black.withOpacity(0.6),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                              Shadow(
                                color: const Color(0xFF1B5E20).withOpacity(0.9),
                                blurRadius: 10,
                                offset: const Offset(0, 3),
                              ),
                            ]
                          : null,
                    ),
                  ),
                  // إضافة وصف خاص لبطاقة الحيوانات والنباتات
                  if (widget.categoryInfo.category == QuizCategory.animals) ...[
                    const SizedBox(height: 5),
                    Text(
                      "استكشف عالم الحيوانات المذهل",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                        fontStyle: FontStyle.italic,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ] else if (widget.categoryInfo.category == QuizCategory.plants) ...[
                    const SizedBox(height: 5),
                    Text(
                      "اكتشف أسرار المملكة النباتية 🌿",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.95),
                        fontStyle: FontStyle.italic,
                        fontWeight: FontWeight.w500,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.4),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                          Shadow(
                            color: const Color(0xFF8BC34A).withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 0),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // تحسين سهم الانتقال للبطاقات الخاصة
            widget.categoryInfo.category == QuizCategory.animals
              ? Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.2),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 18,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                )
              : widget.categoryInfo.category == QuizCategory.plants
                ? Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          const Color(0xFF8BC34A).withOpacity(0.4),
                          const Color(0xFF9CCC65).withOpacity(0.2),
                        ],
                      ),
                      border: Border.all(
                        color: const Color(0xFF8BC34A).withOpacity(0.5),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF8BC34A).withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.eco, // أيقونة أوراق للنباتات
                      color: Colors.white,
                      size: 20,
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.4),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  )
                : const Icon(Icons.arrow_forward_ios, color: Colors.white70),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // بطاقة الحيوانات مع تأثيرات خاصة
    if (widget.categoryInfo.category == QuizCategory.animals) {
      return AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _glowAnimation]),
        builder: (context, child) {
          return InkWell(
            onTap: widget.onTap,
            borderRadius: BorderRadius.circular(20),
            child: Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFF97316).withOpacity(_glowAnimation.value),
                      blurRadius: 35, // زيادة التمويه
                      spreadRadius: 8, // زيادة الانتشار
                    ),
                    // إضافة ظل ثاني للمزيد من التأثير
                    BoxShadow(
                      color: const Color(0xFFFB923C).withOpacity(_glowAnimation.value * 0.7),
                      blurRadius: 50,
                      spreadRadius: 12,
                    ),
                  ],
                ),
                child: _buildCardContent(),
              ),
            ),
          );
        },
      );
    }

    // بطاقة النباتات مع تأثيرات خاصة مختلفة
    if (widget.categoryInfo.category == QuizCategory.plants) {
      return AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _glowAnimation]),
        builder: (context, child) {
          return InkWell(
            onTap: widget.onTap,
            borderRadius: BorderRadius.circular(20),
            child: Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    // ظل ليموني متوهج للنباتات
                    BoxShadow(
                      color: const Color(0xFF8BC34A).withOpacity(_glowAnimation.value * 0.8),
                      blurRadius: 40,
                      spreadRadius: 10,
                    ),
                    // ظل أخضر فاتح
                    BoxShadow(
                      color: const Color(0xFF9CCC65).withOpacity(_glowAnimation.value * 0.6),
                      blurRadius: 55,
                      spreadRadius: 15,
                    ),
                    // ظل أخضر داكن للعمق
                    BoxShadow(
                      color: const Color(0xFF43A047).withOpacity(_glowAnimation.value * 0.4),
                      blurRadius: 25,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: _buildCardContent(),
              ),
            ),
          );
        },
      );
    }

    // البطاقات العادية
    return InkWell(
      onTap: widget.onTap,
      borderRadius: BorderRadius.circular(20),
      child: Ink(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: widget.categoryInfo.gradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, 5)
            )
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
          child: Row(
            children: [
              Icon(
                widget.categoryInfo.icon,
                size: 50,
                color: Colors.white.withOpacity(0.9)
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Text(
                  widget.categoryInfo.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              const Icon(Icons.arrow_forward_ios, color: Colors.white70),
            ],
          ),
        ),
      ),
    );
  }
}


// --- Level Select Screen (Modified) ---
class LevelSelectScreen extends StatefulWidget {
  final String categoryTitle; // Added category title
  final List<LevelModel> levels;
  final AudioService audioService;
  final Function(int, int, int) onLevelComplete;
  final List<Color> backgroundGradient; // Added gradient
  final QuizCategory category; // إضافة فئة المسابقة

  const LevelSelectScreen({
    super.key,
    required this.categoryTitle,
    required this.levels,
    required this.audioService,
    required this.onLevelComplete,
    required this.backgroundGradient,
    required this.category, // طلب فئة المسابقة
  });

  @override
  State<LevelSelectScreen> createState() => _LevelSelectScreenState();
}

class _LevelSelectScreenState extends State<LevelSelectScreen> {
  // إضافة متغير لتتبع آخر مستوى تم فتحه
  int? _recentlyUnlockedLevel;

  // بناء مؤشر القفل المدمج مع متطلبات النجوم
  Widget _buildLockIndicatorCompact(LevelModel level, int index, List<LevelModel> allLevels) {
    final requiredStars = _getRequiredStarsForLevel(index + 1);
    final currentStars = _calculateTotalStars(allLevels);

    if (requiredStars == 0) {
      // مستوى عادي - قفل بسيط
      return Icon(
        Icons.lock,
        color: Colors.white.withOpacity(0.5),
        size: 14,
      );
    }

    // مستوى خاص - قفل مع متطلبات النجوم
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.amber.withOpacity(0.8),
            Colors.orange.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.lock,
            color: Colors.white,
            size: 10,
          ),
          const SizedBox(width: 2),
          Text(
            '$requiredStars',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 8,
              fontWeight: FontWeight.bold,
            ),
          ),
          Icon(
            Icons.star,
            color: Colors.white,
            size: 8,
          ),
        ],
      ),
    );
  }

  // الحصول على متطلبات النجوم للمستوى
  int _getRequiredStarsForLevel(int levelNumber) {
    switch (levelNumber) {
      case 5: return 10;  // تم تعديل المستوى 5 إلى 10 نجوم
      case 10: return 27;
      case 3: return 3;
      case 4: return 6;
      case 6: return 15;
      case 7: return 18;
      case 8: return 21;
      case 9: return 24;
      case 11: return 30;
      case 12: return 33;
      case 13: return 36;
      case 14: return 39;
      case 15: return 42;
      case 16: return 45;
      case 17: return 48;
      case 18: return 51;
      case 19: return 54;
      case 20: return 57;
      default: return 1; // مستوى عادي يحتاج نجمة واحدة على الأقل
    }
  }

  // حساب إجمالي النجوم (نسخة للشاشة)
  int _calculateTotalStars(List<LevelModel> levels) {
    int totalStars = 0;
    for (var level in levels) {
      totalStars += level.stars;
    }
    return totalStars;
  }

  // عرض حوار متطلبات المستوى المقفل
  void _showLevelRequirementsDialog(LevelModel level, int index, List<LevelModel> allLevels) {
    final requiredStars = _getRequiredStarsForLevel(index + 1);
    final currentStars = _calculateTotalStars(allLevels);
    final levelNumber = index + 1;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 600),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, animationValue, child) {
            return Transform.scale(
              scale: 0.3 + (animationValue * 0.7),
              child: Opacity(
                opacity: animationValue,
                child: AlertDialog(
                  backgroundColor: Colors.transparent,
                  content: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.red.shade400,
                          Colors.red.shade600,
                          Colors.red.shade800,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withOpacity(0.5),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                          spreadRadius: 3,
                        ),
                      ],
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // أيقونة القفل
                        Container(
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.2),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.4),
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            Icons.lock,
                            size: 40,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 20),
                        // العنوان
                        Text(
                          '🔒 المستوى مقفل',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.5),
                                blurRadius: 3,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 15),
                        // رقم المستوى
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'المستوى $levelNumber',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        // المتطلبات
                        if (levelNumber == 5) ...[
                          Text(
                            'للوصول للمستوى 5:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 10),
                          Container(
                            padding: const EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'تحتاج إلى ',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.white,
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.amber,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            '$requiredStars',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w800,
                                              color: Colors.white,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          Icon(
                                            Icons.star,
                                            size: 16,
                                            color: Colors.white,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Text(
                                      ' نجمة',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'لديك حالياً: ',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.white,
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: currentStars >= requiredStars ? Colors.green : Colors.orange,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            '$currentStars',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w800,
                                              color: Colors.white,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          Icon(
                                            Icons.star,
                                            size: 16,
                                            color: Colors.white,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Text(
                                      ' نجمة',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ] else ...[
                          Text(
                            requiredStars == 1
                                ? 'تحتاج إلى نجمة واحدة على الأقل من المستوى السابق'
                                : 'تحتاج إلى $requiredStars نجمة لفتح هذا المستوى',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'لديك حالياً: $currentStars',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(width: 5),
                              Icon(
                                Icons.star,
                                color: Colors.yellow.shade300,
                                size: 16,
                              ),
                            ],
                          ),
                        ],
                        const SizedBox(height: 20),
                        // زر الإغلاق
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.red.shade700,
                            padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            elevation: 5,
                          ),
                          child: const Text(
                            'فهمت',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    
    // التحقق من المستوى المفتوح حديثًا من البيانات المخزنة
    _checkForRecentlyUnlockedLevel();
  }

  // التحقق من آخر مستوى تم فتحه
  Future<void> _checkForRecentlyUnlockedLevel() async {
    final prefs = await SharedPreferences.getInstance();
    final key = 'recently_unlocked_${widget.category.name}';
    final recentLevel = prefs.getInt(key);

    // حذف المعلومة بعد استخدامها لمرة واحدة
    if (recentLevel != null) {
      await prefs.remove(key);

      setState(() {
        _recentlyUnlockedLevel = recentLevel;
      });
    }
  }

  // تحديث البيانات من التخزين المحلي
  Future<void> _refreshLevelsData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String key = '';

      // تحديد مفتاح التخزين حسب الفئة
      switch (widget.category) {
        case QuizCategory.islamic:
          key = 'islamic_levels_data';
          break;
        case QuizCategory.math:
          key = 'math_levels_data';
          break;
        case QuizCategory.historical:
          key = 'historical_levels_data';
          break;
        case QuizCategory.videoGames:
          key = 'video_games_levels_data';
          break;
        case QuizCategory.sports:
          key = 'sports_levels_data';
          break;
        case QuizCategory.literature:
          key = 'literature_levels_data';
          break;
        case QuizCategory.animals:
          key = 'animals_levels_data';
          break;
        case QuizCategory.plants:
          key = 'plants_levels_data';
          break;
        case QuizCategory.cars:
          key = 'cars_levels_data';
          break;
      }

      if (key.isNotEmpty) {
        // تحميل البيانات المحدثة
        final String? levelsDataString = prefs.getString(key);
        if (levelsDataString != null) {
          final List<dynamic> decodedData = jsonDecode(levelsDataString);
          final List<Map<String, dynamic>> savedLevels = decodedData.cast<Map<String, dynamic>>();

          // تحديث البيانات في widget.levels
          for (var savedLevel in savedLevels) {
            final levelIndex = widget.levels.indexWhere((level) => level.level == savedLevel['level']);
            if (levelIndex != -1) {
              widget.levels[levelIndex].stars = savedLevel['stars'] ?? 0;
              widget.levels[levelIndex].highScore = savedLevel['highScore'] ?? 0;
              widget.levels[levelIndex].isUnlocked = savedLevel['isUnlocked'] ?? false;
            }
          }

          // التحقق من المستوى المفتوح حديثاً
          await _checkForRecentlyUnlockedLevel();

          // تحديث الواجهة
          if (mounted) {
            setState(() {});
          }

          debugPrint('✅ تم تحديث بيانات المستويات بنجاح');
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات المستويات: $e');
    }
  }

  // إظهار نافذة تأكيد إعادة تصفير المسابقة
  Future<void> _showResetConfirmationDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0)),
          backgroundColor: Colors.white.withOpacity(0.9),
          title: Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.red,
                size: 28,
              ),
              const SizedBox(width: 10),
              Text(
                'تأكيد إعادة الضبط',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.red.shade50,
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.delete_forever,
                      color: Colors.red.shade700,
                      size: 36,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'سيتم حذف جميع النجوم والنقاط والتقدم في مسابقة "${widget.categoryTitle}".\n\nهذا الإجراء لا يمكن التراجع عنه!',
                        style: TextStyle(
                          fontSize: 14,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'إلغاء',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton.icon(
              icon: const Icon(Icons.refresh, color: Colors.white),
              label: const Text('إعادة ضبط'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: () {
                _resetCategoryProgress();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // إعادة تصفير تقدم المسابقة
  Future<void> _resetCategoryProgress() async {
    // عمل نسخة من المستويات وإعادة ضبطها
    List<LevelModel> resetLevels = List.from(widget.levels);
    
    // إعادة تعيين الحالة الافتراضية للمستويات
    for (int i = 0; i < resetLevels.length; i++) {
      resetLevels[i].stars = 0;
      resetLevels[i].highScore = 0;
      resetLevels[i].isUnlocked = (i == 0); // فقط المستوى الأول مفتوح
    }
    
    // حفظ البيانات المعاد تصفيرها في التخزين المحلي
    final prefs = await SharedPreferences.getInstance();
    String key = '';
    
    // تحديد مفتاح التخزين بناءً على الفئة
    switch (widget.category) {
      case QuizCategory.islamic:
        key = 'islamic_levels_data';
        break;
      case QuizCategory.math:
        key = 'math_levels_data';
        break;
      case QuizCategory.historical:
        key = 'historical_levels_data';
        break;
      case QuizCategory.videoGames:
        key = 'video_games_levels_data';
        break;
      case QuizCategory.sports:
        key = 'sports_levels_data';
        break;
      case QuizCategory.literature:
        key = 'literature_levels_data';
        break;
      case QuizCategory.animals:
        key = 'animals_levels_data';
        break;
      case QuizCategory.plants:
        key = 'plants_levels_data';
        break;
      case QuizCategory.cars:
        key = 'cars_levels_data';
        break;
    }
    
    if (key.isNotEmpty) {
      final List<Map<String, dynamic>> levelsData = resetLevels.map((level) => {
        'level': level.level,
        'isUnlocked': level.isUnlocked,
        'stars': level.stars,
        'highScore': level.highScore,
      }).toList();
      
      await prefs.setString(key, jsonEncode(levelsData));
      
      // عرض رسالة تأكيد نجاح العملية
      if (mounted) {
        setState(() {
          // تحديث واجهة المستخدم
          for (int i = 0; i < widget.levels.length; i++) {
            widget.levels[i].stars = 0;
            widget.levels[i].highScore = 0;
            widget.levels[i].isUnlocked = (i == 0);
          }
        });
        
        // عرض رسالة نجاح مؤقتة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 10),
                const Text('تم إعادة ضبط المسابقة بنجاح'),
              ],
            ),
            backgroundColor: Colors.green[700],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  // فتح جميع المستويات
  Future<void> _unlockAllLevels() async {
    // عرض شاشة تأكيد لفتح جميع المستويات
    bool confirm = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0)),
          backgroundColor: Colors.white.withOpacity(0.9),
          title: Row(
            children: [
              Icon(
                Icons.lock_open,
                color: Colors.green,
                size: 28,
              ),
              const SizedBox(width: 10),
              Text(
                'فتح جميع المستويات',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            'هل أنت متأكد أنك تريد فتح جميع المستويات في مسابقة "${widget.categoryTitle}"؟',
            style: TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'إلغاء',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton.icon(
              icon: const Icon(Icons.lock_open, color: Colors.white),
              label: const Text('فتح الكل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;

    if (!confirm) return;

    // عمل نسخة من المستويات وفتحها جميعًا
    List<LevelModel> unlockedLevels = List.from(widget.levels);
    bool changed = false;
    
    // فتح جميع المستويات
    for (int i = 0; i < unlockedLevels.length; i++) {
      if (!unlockedLevels[i].isUnlocked) {
        unlockedLevels[i].isUnlocked = true;
        changed = true;
      }
    }
    
    if (changed) {
      // حفظ البيانات المحدثة في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      String key = '';
      
      // تحديد مفتاح التخزين بناءً على الفئة
      switch (widget.category) {
        case QuizCategory.islamic:
          key = 'islamic_levels_data';
          break;
        case QuizCategory.math:
          key = 'math_levels_data';
          break;
        case QuizCategory.historical:
          key = 'historical_levels_data';
          break;
        case QuizCategory.videoGames:
          key = 'video_games_levels_data';
          break;
        case QuizCategory.sports:
          key = 'sports_levels_data';
          break;
        case QuizCategory.literature:
          key = 'literature_levels_data';
          break;
        case QuizCategory.animals:
          key = 'animals_levels_data';
          break;
        case QuizCategory.plants:
          key = 'plants_levels_data';
          break;
        case QuizCategory.cars:
          key = 'cars_levels_data';
          break;
      }
      
      if (key.isNotEmpty) {
        final List<Map<String, dynamic>> levelsData = unlockedLevels.map((level) => {
          'level': level.level,
          'isUnlocked': level.isUnlocked,
          'stars': level.stars,
          'highScore': level.highScore,
        }).toList();
        
        await prefs.setString(key, jsonEncode(levelsData));
        
        // عرض رسالة تأكيد نجاح العملية
        if (mounted) {
          setState(() {
            // تحديث واجهة المستخدم
            for (int i = 0; i < widget.levels.length; i++) {
              widget.levels[i].isUnlocked = true;
            }
          });
          
          // عرض رسالة نجاح مؤقتة
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 10),
                  const Text('تم فتح جميع المستويات بنجاح'),
                ],
              ),
              backgroundColor: Colors.green[700],
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar( // Added AppBar for back navigation and title
          title: Text(widget.categoryTitle, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          backgroundColor: widget.backgroundGradient[1], // Use a color from the gradient
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.white),
          actions: [
            // إضافة زر إعادة الضبط في شريط التطبيق
            IconButton(
              icon: const Icon(
                Icons.restart_alt,
                color: Colors.white,
              ),
              tooltip: 'إعادة ضبط المسابقة',
              onPressed: _showResetConfirmationDialog,
            ),
            // إضافة زر فتح جميع المستويات في شريط التطبيق
            IconButton(
              icon: const Icon(
                Icons.lock_open,
                color: Colors.white,
              ),
              tooltip: 'فتح جميع المستويات',
              onPressed: _unlockAllLevels,
            ),
          ],
        ),
        body: Container(
          decoration: BoxDecoration( // Use passed gradient
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: widget.backgroundGradient,
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  const SizedBox(height: 10), // Adjusted spacing
                  Text( // Keep "Select Level" text
                    "اختر المستوى",
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          offset: Offset(2, 2),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                  ),
                  
                  // عرض معلومات عن تقدم المسابقة
                  _buildCategoryProgress(),
                  
                  const SizedBox(height: 8),
                  // شبكة المستويات المحسنة لتملأ كامل المساحة المتبقية
                  Expanded(
                    flex: 3, // إعطاء مساحة أكبر للمستويات
                    child: widget.levels.isEmpty
                      ? const Center(child: Text("لا توجد مستويات متاحة حاليًا.", style: TextStyle(color: Colors.white, fontSize: 18)))
                      : LayoutBuilder(
                          builder: (context, constraints) {
                            // حساب الحجم المثالي للمستويات لملء كامل المساحة
                            final availableWidth = constraints.maxWidth - 16; // 16 للحواف
                            final availableHeight = constraints.maxHeight - 10; // 10 للمساحات

                            // حساب حجم كل مستوى ليملأ المساحة بالكامل
                            final itemWidth = (availableWidth - 32) / 5; // 5 مستويات في الصف
                            final itemHeight = (availableHeight - 8) / 2; // صفين
                            final aspectRatio = itemWidth / itemHeight;

                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              child: GridView.count(
                                physics: const NeverScrollableScrollPhysics(),
                                crossAxisCount: 5,
                                childAspectRatio: aspectRatio.clamp(0.7, 1.6), // نطاق أوسع للحجم
                                crossAxisSpacing: 6,
                                mainAxisSpacing: 4,
                                children: List.generate(widget.levels.length, (index) {
                                  final bool isRecentlyUnlocked = widget.levels[index].level == _recentlyUnlockedLevel;
                                  return _buildMaximizedLevelCard(widget.levels[index], index, isRecentlyUnlocked);
                                }),
                              ),
                            );
                          },
                        ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  // عرض ملخص تقدم المسابقة
  Widget _buildCategoryProgress() {
    // حساب إجمالي النجوم والمستويات المفتوحة
    int totalStars = 0;
    int unlockedLevels = 0;
    int highestScore = 0;
    
    for (var level in widget.levels) {
      totalStars += level.stars;
      if (level.isUnlocked) unlockedLevels++;
      if (level.highScore > highestScore) highestScore = level.highScore;
    }
    
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildProgressItem(
            Icons.star,
            const Color(0xFFFFC300),
            "$totalStars",
            "النجوم",
          ),
          _buildProgressItem(
            Icons.lock_open,
            Colors.lightBlueAccent,
            "$unlockedLevels/${widget.levels.length}",
            "المستويات",
          ),
          _buildProgressItem(
            Icons.emoji_events,
            Colors.amber,
            "$highestScore",
            "أعلى نقاط",
          ),
        ],
      ),
    );
  }
  
  // بناء عنصر تقدم فردي
  Widget _buildProgressItem(IconData icon, Color color, String value, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  // بناء بطاقة مستوى مدمجة وصغيرة
  Widget _buildCompactLevelCard(LevelModel level, int index, bool isRecentlyUnlocked) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: level.isUnlocked
                  ? [
                      widget.backgroundGradient[0],
                      widget.backgroundGradient[1],
                    ]
                  : [
                      Colors.grey.withOpacity(0.5),
                      Colors.grey.withOpacity(0.3),
                    ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: level.isUnlocked
                ? [
                    BoxShadow(
                      color: widget.backgroundGradient[0].withOpacity(0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ]
                : [],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: level.isUnlocked
                  ? () {
                      widget.audioService.playClickSound();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => GameScreen(
                            levelData: level,
                            audioService: widget.audioService,
                            onLevelComplete: (stars, score) {
                              widget.onLevelComplete(index, stars, score);
                            },
                            onDataUpdated: _refreshLevelsData, // إضافة callback
                          ),
                        ),
                      );
                    }
                  : () {
                      widget.audioService.playClickSound();
                      _showLevelRequirementsDialog(level, index, widget.levels);
                    },
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // رقم المستوى
                    Text(
                      "${level.level}",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: level.isUnlocked ? const Color(0xFFFCA311) : Colors.white.withOpacity(0.5),
                      ),
                    ),
                    const SizedBox(height: 4),
                    // النجوم
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        3,
                        (starIndex) => Icon(
                          Icons.star,
                          size: 12,
                          color: starIndex < level.stars
                              ? const Color(0xFFFFC300)
                              : Colors.white.withOpacity(0.3),
                        ),
                      ),
                    ),
                    const SizedBox(height: 2),
                    // أيقونة القفل للمستويات المقفلة مع متطلبات النجوم
                    if (!level.isUnlocked)
                      _buildLockIndicatorCompact(level, index, widget.levels),
                  ],
                ),
              ),
            ),
          ),
        ),
        // تأثير للمستوى المفتوح حديثاً - غير قابل للنقر
        if (isRecentlyUnlocked)
          Positioned.fill(
            child: IgnorePointer( // إضافة IgnorePointer لتجاهل النقرات
              child: TweenAnimationBuilder<double>(
                tween: Tween(begin: 0.0, end: 1.0),
                duration: const Duration(seconds: 1),
                builder: (context, value, child) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Color.lerp(
                          Colors.transparent,
                          Colors.amber,
                          (math.sin(value * math.pi * 2) + 1) / 2,
                        )!,
                        width: 2,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
      ],
    );
  }

  // بناء بطاقة مستوى محسنة وأكبر
  Widget _buildEnhancedLevelCard(LevelModel level, int index, bool isRecentlyUnlocked) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: level.isUnlocked
                  ? [
                      widget.backgroundGradient[0],
                      widget.backgroundGradient[1],
                      widget.backgroundGradient[0].withOpacity(0.8),
                    ]
                  : [
                      Colors.grey.withOpacity(0.6),
                      Colors.grey.withOpacity(0.4),
                      Colors.grey.withOpacity(0.3),
                    ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: level.isUnlocked
                  ? Colors.white.withOpacity(0.4)
                  : Colors.white.withOpacity(0.2),
              width: 2,
            ),
            boxShadow: level.isUnlocked
                ? [
                    BoxShadow(
                      color: widget.backgroundGradient[0].withOpacity(0.4),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                      spreadRadius: 1,
                    ),
                    BoxShadow(
                      color: Colors.white.withOpacity(0.1),
                      blurRadius: 1,
                      offset: const Offset(0, 1),
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(16),
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: level.isUnlocked
                  ? () {
                      widget.audioService.playClickSound();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => GameScreen(
                            levelData: level,
                            audioService: widget.audioService,
                            onLevelComplete: (stars, score) {
                              widget.onLevelComplete(index, stars, score);
                            },
                            onDataUpdated: _refreshLevelsData, // إضافة callback
                          ),
                        ),
                      );
                    }
                  : () {
                      // تأثير اهتزاز للمستويات المقفلة
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text("يجب إكمال المستوى السابق أولاً"),
                          backgroundColor: Colors.orange,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // دائرة رقم المستوى مع تأثيرات
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: level.isUnlocked
                              ? [
                                  Colors.white.withOpacity(0.3),
                                  Colors.white.withOpacity(0.1),
                                ]
                              : [
                                  Colors.grey.withOpacity(0.3),
                                  Colors.grey.withOpacity(0.1),
                                ],
                        ),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: level.isUnlocked
                              ? Colors.white.withOpacity(0.5)
                              : Colors.white.withOpacity(0.2),
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          "${level.level}",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: level.isUnlocked
                                ? const Color(0xFFFCA311)
                                : Colors.white.withOpacity(0.6),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // النجوم مع تأثيرات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        3,
                        (starIndex) => Container(
                          margin: const EdgeInsets.symmetric(horizontal: 1),
                          child: Icon(
                            Icons.star,
                            size: 14,
                            color: starIndex < level.stars
                                ? const Color(0xFFFFC300)
                                : Colors.white.withOpacity(0.3),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    // أيقونة القفل أو النقاط
                    if (!level.isUnlocked)
                      Icon(
                        Icons.lock,
                        color: Colors.white.withOpacity(0.5),
                        size: 16,
                      )
                    else if (level.highScore > 0)
                      Text(
                        "${level.highScore}",
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
        // تأثير للمستوى المفتوح حديثاً محسن - غير قابل للنقر
        if (isRecentlyUnlocked)
          Positioned.fill(
            child: IgnorePointer( // إضافة IgnorePointer لتجاهل النقرات
              child: TweenAnimationBuilder<double>(
                tween: Tween(begin: 0.0, end: 1.0),
                duration: const Duration(seconds: 2),
                builder: (context, value, child) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Color.lerp(
                          Colors.transparent,
                          Colors.amber,
                          (math.sin(value * math.pi * 4) + 1) / 2,
                        )!,
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Color.lerp(
                            Colors.transparent,
                            Colors.amber.withOpacity(0.8),
                            (math.sin(value * math.pi * 4) + 1) / 2,
                          )!,
                          blurRadius: 15,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
      ],
    );
  }

  // بناء بطاقة مستوى محسنة لملء المساحة
  Widget _buildOptimizedLevelCard(LevelModel level, int index, bool isRecentlyUnlocked) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: level.isUnlocked
                  ? [
                      widget.backgroundGradient[0],
                      widget.backgroundGradient[1],
                      widget.backgroundGradient[0].withOpacity(0.8),
                    ]
                  : [
                      Colors.grey.withOpacity(0.6),
                      Colors.grey.withOpacity(0.4),
                      Colors.grey.withOpacity(0.3),
                    ],
            ),
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: level.isUnlocked
                  ? Colors.white.withOpacity(0.4)
                  : Colors.white.withOpacity(0.2),
              width: 1.5,
            ),
            boxShadow: level.isUnlocked
                ? [
                    BoxShadow(
                      color: widget.backgroundGradient[0].withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                      spreadRadius: 1,
                    ),
                    BoxShadow(
                      color: Colors.white.withOpacity(0.1),
                      blurRadius: 1,
                      offset: const Offset(0, 1),
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(14),
            child: InkWell(
              borderRadius: BorderRadius.circular(14),
              onTap: level.isUnlocked
                  ? () {
                      widget.audioService.playClickSound();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => GameScreen(
                            levelData: level,
                            audioService: widget.audioService,
                            onLevelComplete: (stars, score) {
                              widget.onLevelComplete(index, stars, score);
                            },
                            onDataUpdated: _refreshLevelsData, // إضافة callback
                          ),
                        ),
                      );
                    }
                  : () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text("يجب إكمال المستوى السابق أولاً"),
                          backgroundColor: Colors.orange,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // دائرة رقم المستوى متجاوبة
                    Flexible(
                      flex: 3,
                      child: AspectRatio(
                        aspectRatio: 1.0,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: level.isUnlocked
                                  ? [
                                      Colors.white.withOpacity(0.3),
                                      Colors.white.withOpacity(0.1),
                                    ]
                                  : [
                                      Colors.grey.withOpacity(0.3),
                                      Colors.grey.withOpacity(0.1),
                                    ],
                            ),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: level.isUnlocked
                                  ? Colors.white.withOpacity(0.5)
                                  : Colors.white.withOpacity(0.2),
                              width: 1.5,
                            ),
                          ),
                          child: Center(
                            child: FittedBox(
                              child: Text(
                                "${level.level}",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: level.isUnlocked
                                      ? const Color(0xFFFCA311)
                                      : Colors.white.withOpacity(0.6),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    // النجوم متجاوبة
                    Flexible(
                      flex: 1,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          3,
                          (starIndex) => Flexible(
                            child: Icon(
                              Icons.star,
                              size: 12,
                              color: starIndex < level.stars
                                  ? const Color(0xFFFFC300)
                                  : Colors.white.withOpacity(0.3),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 2),
                    // أيقونة القفل أو النقاط
                    Flexible(
                      flex: 1,
                      child: !level.isUnlocked
                          ? Icon(
                              Icons.lock,
                              color: Colors.white.withOpacity(0.5),
                              size: 14,
                            )
                          : level.highScore > 0
                              ? FittedBox(
                                  child: Text(
                                    "${level.highScore}",
                                    style: TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white.withOpacity(0.8),
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        // تأثير للمستوى المفتوح حديثاً - غير قابل للنقر
        if (isRecentlyUnlocked)
          Positioned.fill(
            child: IgnorePointer( // إضافة IgnorePointer لتجاهل النقرات
              child: TweenAnimationBuilder<double>(
                tween: Tween(begin: 0.0, end: 1.0),
                duration: const Duration(seconds: 2),
                builder: (context, value, child) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: Color.lerp(
                          Colors.transparent,
                          Colors.amber,
                          (math.sin(value * math.pi * 4) + 1) / 2,
                        )!,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Color.lerp(
                            Colors.transparent,
                            Colors.amber.withOpacity(0.6),
                            (math.sin(value * math.pi * 4) + 1) / 2,
                          )!,
                          blurRadius: 10,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
      ],
    );
  }

  // بناء بطاقة مستوى مكبرة لملء كامل المساحة مع تأثيرات جمالية
  Widget _buildMaximizedLevelCard(LevelModel level, int index, bool isRecentlyUnlocked) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 600 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, animationValue, child) {
        return Transform.scale(
          scale: 0.7 + (animationValue * 0.3),
          child: Opacity(
            opacity: animationValue,
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: level.isUnlocked
                          ? [
                              widget.backgroundGradient[0],
                              widget.backgroundGradient[1],
                              widget.backgroundGradient[0].withOpacity(0.8),
                              widget.backgroundGradient[1].withOpacity(0.6),
                            ]
                          : [
                              Colors.grey.withOpacity(0.7),
                              Colors.grey.withOpacity(0.5),
                              Colors.grey.withOpacity(0.4),
                              Colors.grey.withOpacity(0.3),
                            ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: level.isUnlocked
                          ? Colors.white.withOpacity(0.5)
                          : Colors.white.withOpacity(0.2),
                      width: 2,
                    ),
                    boxShadow: level.isUnlocked
                        ? [
                            BoxShadow(
                              color: widget.backgroundGradient[0].withOpacity(0.4),
                              blurRadius: 12,
                              offset: const Offset(0, 6),
                              spreadRadius: 2,
                            ),
                            BoxShadow(
                              color: Colors.white.withOpacity(0.2),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ]
                        : [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 6,
                              offset: const Offset(0, 3),
                            ),
                          ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: level.isUnlocked
                          ? () {
                              widget.audioService.playClickSound();
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => GameScreen(
                                    levelData: level,
                                    audioService: widget.audioService,
                                    onLevelComplete: (stars, score) {
                                      widget.onLevelComplete(index, stars, score);
                                    },
                                    onDataUpdated: _refreshLevelsData, // إضافة callback
                                  ),
                                ),
                              );
                            }
                          : () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: const Text("يجب إكمال المستوى السابق أولاً"),
                                  backgroundColor: Colors.orange,
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                      child: Stack(
                        children: [
                          // تأثير لمعان متحرك للمستويات المفتوحة
                          if (level.isUnlocked)
                            Positioned.fill(
                              child: TweenAnimationBuilder<double>(
                                duration: const Duration(seconds: 4),
                                tween: Tween(begin: 0.0, end: 1.0),
                                builder: (context, value, child) {
                                  return Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                      gradient: LinearGradient(
                                        begin: Alignment(-1.0 + (value * 2), -1.0),
                                        end: Alignment(1.0 + (value * 2), 1.0),
                                        colors: [
                                          Colors.transparent,
                                          Colors.white.withOpacity(0.15),
                                          Colors.transparent,
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          // المحتوى الرئيسي
                          Padding(
                            padding: const EdgeInsets.all(10),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // دائرة رقم المستوى مكبرة
                                Flexible(
                                  flex: 4,
                                  child: AspectRatio(
                                    aspectRatio: 1.0,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        gradient: RadialGradient(
                                          colors: level.isUnlocked
                                              ? [
                                                  Colors.white.withOpacity(0.4),
                                                  Colors.white.withOpacity(0.2),
                                                  Colors.white.withOpacity(0.1),
                                                ]
                                              : [
                                                  Colors.grey.withOpacity(0.4),
                                                  Colors.grey.withOpacity(0.2),
                                                  Colors.grey.withOpacity(0.1),
                                                ],
                                        ),
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: level.isUnlocked
                                              ? Colors.white.withOpacity(0.6)
                                              : Colors.white.withOpacity(0.3),
                                          width: 2,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: level.isUnlocked
                                                ? Colors.white.withOpacity(0.3)
                                                : Colors.black.withOpacity(0.2),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Center(
                                        child: TweenAnimationBuilder<double>(
                                          duration: const Duration(seconds: 2),
                                          tween: Tween(begin: 0.0, end: 1.0),
                                          builder: (context, value, child) {
                                            return Transform.scale(
                                              scale: level.isUnlocked
                                                  ? 1.0 + (math.sin(value * math.pi * 2) * 0.05)
                                                  : 1.0,
                                              child: FittedBox(
                                                child: Text(
                                                  "${level.level}",
                                                  style: TextStyle(
                                                    fontSize: 20,
                                                    fontWeight: FontWeight.bold,
                                                    color: level.isUnlocked
                                                        ? const Color(0xFFFCA311)
                                                        : Colors.white.withOpacity(0.6),
                                                    shadows: [
                                                      Shadow(
                                                        color: Colors.black.withOpacity(0.3),
                                                        blurRadius: 2,
                                                        offset: const Offset(0, 1),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 6),
                                // النجوم مكبرة
                                Flexible(
                                  flex: 1,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: List.generate(
                                      3,
                                      (starIndex) => Flexible(
                                        child: Container(
                                          margin: const EdgeInsets.symmetric(horizontal: 1),
                                          child: TweenAnimationBuilder<double>(
                                            duration: Duration(milliseconds: 800 + (starIndex * 200)),
                                            tween: Tween(begin: 0.0, end: 1.0),
                                            builder: (context, value, child) {
                                              return Transform.scale(
                                                scale: starIndex < level.stars
                                                    ? 0.8 + (value * 0.2)
                                                    : 0.8,
                                                child: Icon(
                                                  Icons.star,
                                                  size: 16,
                                                  color: starIndex < level.stars
                                                      ? const Color(0xFFFFC300)
                                                      : Colors.white.withOpacity(0.3),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                // أيقونة القفل أو النقاط
                                Flexible(
                                  flex: 1,
                                  child: !level.isUnlocked
                                      ? Icon(
                                          Icons.lock,
                                          color: Colors.white.withOpacity(0.6),
                                          size: 16,
                                        )
                                      : level.highScore > 0
                                          ? Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                              decoration: BoxDecoration(
                                                color: Colors.white.withOpacity(0.2),
                                                borderRadius: BorderRadius.circular(8),
                                              ),
                                              child: FittedBox(
                                                child: Text(
                                                  "${level.highScore}",
                                                  style: TextStyle(
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w700,
                                                    color: Colors.white.withOpacity(0.9),
                                                  ),
                                                ),
                                              ),
                                            )
                                          : const SizedBox.shrink(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // تأثير للمستوى المفتوح حديثاً محسن - غير قابل للنقر
                if (isRecentlyUnlocked)
                  Positioned.fill(
                    child: IgnorePointer( // إضافة IgnorePointer لتجاهل النقرات
                      child: TweenAnimationBuilder<double>(
                        tween: Tween(begin: 0.0, end: 1.0),
                        duration: const Duration(seconds: 3),
                        builder: (context, value, child) {
                          return Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Color.lerp(
                                  Colors.transparent,
                                  Colors.amber,
                                  (math.sin(value * math.pi * 6) + 1) / 2,
                                )!,
                                width: 3,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Color.lerp(
                                    Colors.transparent,
                                    Colors.amber.withOpacity(0.8),
                                    (math.sin(value * math.pi * 6) + 1) / 2,
                                  )!,
                                  blurRadius: 20,
                                  spreadRadius: 3,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Removed AppTitle Widget as it's replaced by AppBar title

// --- Level Card (Modified slightly for color) ---
class LevelCard extends StatelessWidget {
  final LevelModel levelData;
  final VoidCallback? onTap;
  final Color cardColor; // Added color parameter

  const LevelCard({
    super.key,
    required this.levelData,
    this.onTap,
    required this.cardColor, // Require color
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: levelData.isUnlocked
            ? cardColor // Use passed color
            : Colors.grey.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          if (levelData.isUnlocked)
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: levelData.isUnlocked ? onTap : null,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "المستوى ${levelData.level}",
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFFCA311), // Keep star color consistent for now
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  levelData.title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                  maxLines: 2, // Allow wrapping
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    3,
                    (index) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2.0),
                      child: Icon(
                        Icons.star,
                        size: 18,
                        color: index < levelData.stars
                            ? const Color(0xFFFFC300) // Keep star color consistent
                            : Colors.white.withOpacity(0.3),
                      ),
                    ),
                  ),
                ),
                 if (levelData.highScore > 0) // Show high score if available
                   Padding(
                     padding: const EdgeInsets.only(top: 4.0),
                     child: Text(
                       "أعلى نقاط: ${levelData.highScore}",
                       style: TextStyle(
                         fontSize: 10,
                         color: Colors.white.withOpacity(0.7),
                       ),
                     ),
                   ),
                if (!levelData.isUnlocked)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Icon(
                      Icons.lock,
                      color: Colors.white.withOpacity(0.7),
                      size: 20,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// --- Game Screen (No major changes needed for structure, but check logic) ---
class GameScreen extends StatefulWidget {
  final LevelModel levelData;
  final AudioService audioService;
  final Function(int, int) onLevelComplete;
  final VoidCallback? onDataUpdated; // إضافة callback لتحديث البيانات

  const GameScreen({
    super.key,
    required this.levelData,
    required this.audioService,
    required this.onLevelComplete,
    this.onDataUpdated, // إضافة المعامل الاختياري
  });

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  int questionIndex = 0;
  int score = 0;
  int currentStreak = 0;
  int timeLeft = 30;
  bool helpFiftyFiftyAvailable = true;
  bool helpSkipAvailable = true;
  bool helpExtraTimeAvailable = true;
  bool isQuestionActive = false;
  double progress = 0.0;
  // إضافة متغير لتتبع عدد الإجابات الصحيحة بشكل مباشر
  int correctAnswersCount = 0;
  // إضافة قائمة لتتبع حالة كل سؤال (صح/خطأ/لم يجب)
  List<int> questionResults = []; // 0 = لم يجب، 1 = صحيح، -1 = خطأ
  // متغيرات تأثيرات الجسيمات
  bool showSuccessParticles = false;
  bool showErrorShake = false;
  // متغيرات تأثيرات إضافية
  bool showSuccessBurst = false;
  bool showStreakMessage = false;
  int consecutiveCorrect = 0;
  
  late Timer timer;
  List<String> shuffledOptions = [];
  List<int> hiddenOptionIndices = [];
  
  late AnimationController _timerAnimationController;
  late Animation<Color?> _timerColorAnimation;
  late AnimationController _questionAnimationController;
  late Animation<double> _questionSlideAnimation;
  late Animation<double> _questionFadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _timerAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true);

    _timerColorAnimation = ColorTween(
      begin: Colors.white,
      end: Colors.red,
    ).animate(_timerAnimationController);

    // إعداد رسوم متحركة للأسئلة
    _questionAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _questionSlideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _questionAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _questionFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _questionAnimationController,
      curve: Curves.easeInOut,
    ));

    // تهيئة قائمة نتائج الأسئلة
    questionResults = List.filled(widget.levelData.questions.length, 0);

    // بدء عرض السؤال الأول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      startQuestion();
    });
  }

  void startQuestion() {
    if (questionIndex >= widget.levelData.questions.length) {
      completeLevel();
      return;
    }

    // إعادة تعيين حالة السؤال
    setState(() {
      timeLeft = 30;
      isQuestionActive = true;
      hiddenOptionIndices = [];

      // خلط الخيارات للسؤال الحالي
      shuffledOptions = List.from(widget.levelData.questions[questionIndex].options);
      shuffledOptions.shuffle();

      // تحديث نسبة التقدم
      progress = questionIndex / widget.levelData.questions.length;
    });

    // تشغيل رسوم متحركة للسؤال الجديد
    _questionAnimationController.reset();
    _questionAnimationController.forward();

    // بدء المؤقت
    startTimer();
  }

  void startTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (timeLeft > 0) {
            timeLeft--;
            
            // تشغيل صوت تحذير عند وصول المؤقت إلى 10 ثوانٍ
            if (timeLeft == 10) {
              widget.audioService.playTimerWarningSound();
            }
          } else {
            handleTimeout();
          }
        });
      }
    });
  }

  void handleTimeout() {
    if (!isQuestionActive) return;
    
    // إيقاف المؤقت
    timer.cancel();
    
    setState(() {
      isQuestionActive = false;
      currentStreak = 0;
    });
    
    // تشغيل صوت الإجابة الخاطئة
    widget.audioService.playIncorrectSound();
    
    // الانتقال للسؤال التالي بعد تأخير
    Future.delayed(const Duration(milliseconds: 2500), () {
      proceedToNextQuestion();
    });
  }

  void handleAnswer(String selectedOption) {
    if (!isQuestionActive) return;
    
    final correctAnswer = widget.levelData.questions[questionIndex].correctAnswer;
    final isCorrect = selectedOption == correctAnswer;
    
    // إيقاف المؤقت
    timer.cancel();
    
    setState(() {
      isQuestionActive = false;
    });
    
    if (isCorrect) {
      // زيادة عدد الإجابات الصحيحة
      correctAnswersCount++;
      questionResults[questionIndex] = 1; // تسجيل إجابة صحيحة

      // حساب النقاط: كل سؤال صحيح = 10 نقاط فقط
      score += 10;

      // زيادة عداد الإجابات المتتالية أولاً
      currentStreak++;

      // تشغيل صوت الإجابة الصحيحة بعد صوت النقر بثانية
      Future.delayed(const Duration(milliseconds: 1000), () {
        _playSuccessSoundImmediate();
      });

      // إضافة تأثير اهتزاز للنجاح (إذا كان متاحاً)
      _triggerSuccessHaptic();

      // إضافة تأثير جسيمات النجاح
      _showSuccessEffect();

      // تأثيرات إضافية للإنجازات المتتالية
      consecutiveCorrect++;
      if (consecutiveCorrect >= 3) {
        _showStreakMessage();
      }
      if (consecutiveCorrect >= 5) {
        _showSpecialSuccessEffect();
      }

    } else {
      // إعادة تعيين العد المتتالي
      currentStreak = 0;
      consecutiveCorrect = 0; // إعادة تعيين العداد المتتالي
      questionResults[questionIndex] = -1; // تسجيل إجابة خاطئة

      // تشغيل صوت الإجابة الخاطئة مع تأخير قصير
      Future.delayed(const Duration(milliseconds: 150), () {
        widget.audioService.playIncorrectSound();
      });

      // إضافة تأثير اهتزاز للخطأ
      _triggerErrorHaptic();

      // إضافة تأثير الخطأ
      _showErrorEffect();
    }
    
    // الانتقال للسؤال التالي بعد تأخير
    Future.delayed(const Duration(milliseconds: 2500), () {
      proceedToNextQuestion();
    });
  }

  void proceedToNextQuestion() {
    if (!mounted) return;
    
    setState(() {
      questionIndex++;
    });
    
    startQuestion();
  }

  void completeLevel() {
    if (!mounted) return;
    
    // إيقاف المؤقت
    timer.cancel();
    
    // استخدام عدد الإجابات الصحيحة المتتبع مباشرة
    debugPrint('عدد الإجابات الصحيحة: $correctAnswersCount');
    
    // حساب عدد النجوم حسب المتطلبات الجديدة
    int stars = 0;
    if (correctAnswersCount >= 10) {
      stars = 3; // ثلاث نجوم للحصول على 10 إجابات صحيحة أو أكثر
    } else if (correctAnswersCount >= 6) {
      stars = 2; // نجمتان للحصول على 6-9 إجابات صحيحة
    } else if (correctAnswersCount >= 3) {
      stars = 1; // نجمة واحدة للحصول على 3-5 إجابات صحيحة
    }
    
    // طباعة معلومات تشخيصية
    debugPrint('النقاط: $score، النجوم: $stars');
    
    // تشغيل صوت إكمال المستوى
    widget.audioService.playLevelCompleteSound();

    // عرض شاشة الفوز الجديدة
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => VictoryScreen(
          levelData: widget.levelData,
          score: score,
          starsEarned: stars,
          correctAnswers: correctAnswersCount,
          audioService: widget.audioService,
          onLevelComplete: widget.onLevelComplete,
          onDataUpdated: widget.onDataUpdated, // تمرير callback
        ),
      ),
    );
  }

  void useFiftyFifty() {
    if (!isQuestionActive || !helpFiftyFiftyAvailable) return;
    
    widget.audioService.playClickSound();
    
    setState(() {
      helpFiftyFiftyAvailable = false;
    });
    
    // الحصول على الإجابة الصحيحة
    final correctAnswer = widget.levelData.questions[questionIndex].correctAnswer;
    
    // تجميع مؤشرات الخيارات غير الصحيحة
    List<int> incorrectIndices = [];
    for (int i = 0; i < shuffledOptions.length; i++) {
      if (shuffledOptions[i] != correctAnswer) {
        incorrectIndices.add(i);
      }
    }
    
    // خلط المؤشرات واختيار خيارين
    incorrectIndices.shuffle();
    
    // حذف خيارين خاطئين
    setState(() {
      hiddenOptionIndices = incorrectIndices.sublist(0, 2);
    });
  }

  void useSkip() {
    if (!isQuestionActive || !helpSkipAvailable) return;
    
    widget.audioService.playClickSound();
    
    setState(() {
      helpSkipAvailable = false;
      isQuestionActive = false;
    });
    
    // إيقاف المؤقت
    timer.cancel();
    
    // إعادة تعيين العد المتتالي
    currentStreak = 0;
    
    // الانتقال للسؤال التالي بعد تأخير
    Future.delayed(const Duration(milliseconds: 1500), () {
      proceedToNextQuestion();
    });
  }

  void useExtraTime() {
    if (!isQuestionActive || !helpExtraTimeAvailable) return;

    widget.audioService.playClickSound();

    setState(() {
      helpExtraTimeAvailable = false;
      timeLeft += 15; // إضافة 15 ثانية
    });
  }

  // تشغيل صوت النجاح فوري ومضمون - صوت واحد فقط
  void _playSuccessSoundImmediate() {
    debugPrint('🔊 بدء تشغيل صوت النجاح');

    // تشغيل صوت الإجابة الصحيحة دائماً (إلغاء صوت المتتالية)
    debugPrint('🎵 تشغيل صوت الإجابة الصحيحة');
    widget.audioService.playCorrectSound();
    widget.audioService.playCorrectSound(); // تشغيل مضاعف للضمان

    debugPrint('✅ تم تشغيل الصوت مباشرة');
  }

  // إعادة تهيئة خدمة الصوت
  Future<void> _reinitializeAudio() async {
    try {
      debugPrint('🔄 إعادة تهيئة خدمة الصوت');
      await widget.audioService.initialize();
      debugPrint('✅ تم إعادة تهيئة خدمة الصوت بنجاح');
    } catch (e) {
      debugPrint('❌ فشل في إعادة تهيئة خدمة الصوت: $e');
    }
  }

  // تأثيرات الاهتزاز للتفاعل
  void _triggerSuccessHaptic() {
    try {
      // يمكن إضافة مكتبة الاهتزاز هنا لاحقاً
      // HapticFeedback.lightImpact();
    } catch (e) {
      // تجاهل الأخطاء إذا لم تكن مدعومة
    }
  }

  void _triggerErrorHaptic() {
    try {
      // يمكن إضافة مكتبة الاهتزاز هنا لاحقاً
      // HapticFeedback.heavyImpact();
    } catch (e) {
      // تجاهل الأخطاء إذا لم تكن مدعومة
    }
  }

  // إظهار رسالة الإنجاز المتتالي
  void _showStreakMessage() {
    setState(() {
      showStreakMessage = true;
    });

    // إخفاء الرسالة بعد ثانية واحدة
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        setState(() {
          showStreakMessage = false;
        });
      }
    });
  }

  // إظهار تأثير النجاح
  void _showSuccessEffect() {
    setState(() {
      showSuccessParticles = true;
    });

    // إخفاء التأثير بعد ثانية واحدة
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        setState(() {
          showSuccessParticles = false;
        });
      }
    });
  }

  // إظهار تأثير الخطأ
  void _showErrorEffect() {
    setState(() {
      showErrorShake = true;
    });

    // إخفاء التأثير بعد نصف ثانية
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          showErrorShake = false;
        });
      }
    });
  }

  // إظهار تأثير خاص للإنجازات المتتالية
  void _showSpecialSuccessEffect() {
    setState(() {
      showSuccessBurst = true;
    });

    // إخفاء التأثير بعد ثانيتين
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        setState(() {
          showSuccessBurst = false;
        });
      }
    });

    // إعادة تعيين العداد
    consecutiveCorrect = 0;
  }

  // بناء أزرار مساعدة مدمجة
  Widget _buildCompactHelpButton({
    required IconData icon,
    required String label,
    required bool isAvailable,
    required VoidCallback? onPressed,
  }) {
    return SizedBox(
      height: 45,
      child: ElevatedButton(
        onPressed: isAvailable ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: isAvailable
              ? const Color(0xFFFB8500)
              : Colors.grey.withOpacity(0.4),
          foregroundColor: Colors.white,
          disabledBackgroundColor: Colors.grey.withOpacity(0.4),
          disabledForegroundColor: Colors.white.withOpacity(0.5),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          textStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: isAvailable ? Colors.white.withOpacity(0.8) : Colors.grey.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          elevation: isAvailable ? 4 : 1,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16),
            const SizedBox(height: 2),
            Text(
              label,
              style: const TextStyle(fontSize: 10),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // بناء شريط التقدم المحسن مع أيقونات
  Widget _buildEnhancedProgressBar() {
    return Column(
      children: [
        // شريط التقدم الرئيسي
        Container(
          height: 8,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FractionallySizedBox(
            widthFactor: progress,
            alignment: Alignment.centerLeft,
            child: Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF4CAF50), // أخضر
                    Color(0xFF8BC34A),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        // شريط الأيقونات
        Container(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            children: List.generate(widget.levelData.questions.length, (index) {
              return Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 1),
                  decoration: BoxDecoration(
                    color: _getQuestionStatusColor(index),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: index == questionIndex
                          ? Colors.white
                          : Colors.transparent,
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: _getQuestionStatusIcon(index),
                  ),
                ),
              );
            }),
          ),
        ),
        const SizedBox(height: 8),
        // نص التقدم
        Text(
          "السؤال ${questionIndex + 1} من ${widget.levelData.questions.length}",
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  // تحديد لون حالة السؤال
  Color _getQuestionStatusColor(int index) {
    if (index < questionIndex) {
      // سؤال مكتمل
      return questionResults[index] == 1
          ? const Color(0xFF4CAF50) // أخضر للصحيح
          : const Color(0xFFE53935); // أحمر للخطأ
    } else if (index == questionIndex) {
      // السؤال الحالي
      return const Color(0xFFFFC107); // أصفر للحالي
    } else {
      // سؤال لم يتم الوصول إليه
      return Colors.white.withOpacity(0.3);
    }
  }

  // تحديد أيقونة حالة السؤال
  Widget _getQuestionStatusIcon(int index) {
    if (index < questionIndex) {
      // سؤال مكتمل
      return Icon(
        questionResults[index] == 1
            ? Icons.check
            : Icons.close,
        color: Colors.white,
        size: 16,
      );
    } else if (index == questionIndex) {
      // السؤال الحالي
      return Text(
        "${index + 1}",
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      );
    } else {
      // سؤال لم يتم الوصول إليه
      return Text(
        "${index + 1}",
        style: TextStyle(
          color: Colors.white.withOpacity(0.6),
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      );
    }
  }

  // بناء بطاقة السؤال مع إطار جميل ورسوم متحركة
  Widget _buildQuestionCard() {
    return AnimatedBuilder(
      animation: _questionAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _questionSlideAnimation.value),
          child: Opacity(
            opacity: _questionFadeAnimation.value,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 8),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.15),
                    Colors.white.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // رقم السؤال مع تأثير متوهج
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.amber.withOpacity(0.8),
                          Colors.orange.withOpacity(0.6),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.quiz,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          "السؤال ${questionIndex + 1}",
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // نص السؤال
                  widget.levelData.category == QuizCategory.math
                      ? Directionality(
                          textDirection: TextDirection.ltr, // Force LTR for math
                          child: Text(
                            widget.levelData.questions[questionIndex].question,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              height: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                      : Text( // Default RTL for other categories
                          widget.levelData.questions[questionIndex].question,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء خيارات الإجابة المحسنة مع تأثيرات لكل سؤال
  Widget _buildAnswerOptions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        key: ValueKey('question_$questionIndex'), // مفتاح فريد لكل سؤال
        children: List.generate(shuffledOptions.length, (index) {
          final option = shuffledOptions[index];
          final isHidden = hiddenOptionIndices.contains(index);

          if (isHidden) {
            return const SizedBox.shrink();
          }

          final isCorrect = option == widget.levelData.questions[questionIndex].correctAnswer;

          return AnimatedContainer(
            key: ValueKey('option_${questionIndex}_$index'), // مفتاح فريد لكل خيار
            duration: Duration(milliseconds: 300 + (index * 100)),
            curve: Curves.easeOutBack,
            width: double.infinity,
            margin: EdgeInsets.only(bottom: 12, top: index * 2.0),
            child: TweenAnimationBuilder<double>(
              key: ValueKey('animation_${questionIndex}_$index'), // مفتاح فريد للرسوم المتحركة
              duration: Duration(milliseconds: 500 + (index * 150)),
              tween: Tween<double>(begin: 0.0, end: 1.0),
              builder: (context, double value, child) {
                return Transform.translate(
                  offset: Offset((1 - value) * 100, 0),
                  child: Opacity(
                    opacity: value,
                    child: OptionButton(
                      text: option,
                      onPressed: isQuestionActive ? () {
                        widget.audioService.playClickSound();
                        handleAnswer(option);
                      } : null,
                      isCorrectAnswer: !isQuestionActive && isCorrect,
                      isIncorrectSelected: !isQuestionActive && !isCorrect,
                    ),
                  ),
                );
              },
            ),
          );
        }),
      ),
    );
  }

  // بناء خيارات الإجابة القابلة للتمرير
  Widget _buildScrollableAnswerOptions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        children: List.generate(shuffledOptions.length, (index) {
          final option = shuffledOptions[index];
          final isHidden = hiddenOptionIndices.contains(index);

          if (isHidden) {
            return const SizedBox.shrink();
          }

          final isCorrect = option == widget.levelData.questions[questionIndex].correctAnswer;

          return Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 12),
            child: OptionButton(
              text: option,
              onPressed: isQuestionActive ? () {
                widget.audioService.playClickSound();
                handleAnswer(option);
              } : null,
              isCorrectAnswer: !isQuestionActive && isCorrect,
              isIncorrectSelected: !isQuestionActive && !isCorrect,
            ),
          );
        }),
      ),
    );
  }

  // بناء صف أزرار المساعدة وزر العودة في الأعلى لتجنب التداخل
  Widget _buildTopControlsRow() {
    return Container(
      height: 45,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          // أزرار المساعدة
          Expanded(
            flex: 3,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildTopHelpButton(
                  icon: Icons.content_cut,
                  label: "50/50",
                  isAvailable: helpFiftyFiftyAvailable,
                  onPressed: useFiftyFifty,
                  color: const Color(0xFFE91E63),
                ),
                _buildTopHelpButton(
                  icon: Icons.skip_next,
                  label: "تخطي",
                  isAvailable: helpSkipAvailable,
                  onPressed: useSkip,
                  color: const Color(0xFF2196F3),
                ),
                _buildTopHelpButton(
                  icon: Icons.timer,
                  label: "+15ث",
                  isAvailable: helpExtraTimeAvailable,
                  onPressed: useExtraTime,
                  color: const Color(0xFF4CAF50),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          // زر العودة مدمج
          Expanded(
            flex: 2,
            child: _buildCompactBackButton(),
          ),
        ],
      ),
    );
  }

  // بناء زر مساعدة في الأعلى
  Widget _buildTopHelpButton({
    required IconData icon,
    required String label,
    required bool isAvailable,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        height: 40,
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: ElevatedButton(
          onPressed: isAvailable ? onPressed : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: isAvailable ? color : Colors.grey.withOpacity(0.6),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: isAvailable ? 4 : 1,
            shadowColor: isAvailable ? color.withOpacity(0.5) : Colors.transparent,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 14,
                color: Colors.white,
              ),
              const SizedBox(width: 3),
              Flexible(
                child: Text(
                  label,
                  style: const TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // وظيفة العودة الآمنة مع حفظ التقدم - محسنة للمسابقة
  Future<void> _safeNavigateBack() async {
    try {
      debugPrint('🔙 بدء العودة الآمنة من المسابقة...');

      // إيقاف المؤقت أولاً
      if (timer.isActive) {
        timer.cancel();
        debugPrint('⏹️ تم إيقاف المؤقت');
      }

      // حفظ التقدم الحالي قبل العودة إذا كان هناك تقدم
      if (correctAnswersCount > 0) {
        debugPrint('💾 حفظ التقدم قبل العودة: $correctAnswersCount إجابات صحيحة');

        try {
          // حساب النجوم والنقاط
          final stars = _calculateStars(correctAnswersCount);
          final score = _calculateScore();

          debugPrint('⭐ النجوم المحسوبة: $stars, النقاط: $score');

          // استدعاء وظيفة onLevelComplete لحفظ التقدم
          widget.onLevelComplete(stars, score);

          // انتظار قصير للتأكد من اكتمال الحفظ
          await Future.delayed(const Duration(milliseconds: 500));

          debugPrint('✅ تم حفظ التقدم بنجاح');

        } catch (saveError) {
          debugPrint('❌ خطأ في حفظ التقدم: $saveError');
          // المتابعة بالعودة حتى لو فشل الحفظ
        }
      } else {
        debugPrint('ℹ️ لا يوجد تقدم للحفظ');
      }

      // التحقق من أن الشاشة ما زالت موجودة
      if (!mounted) {
        debugPrint('⚠️ الشاشة لم تعد موجودة');
        return;
      }

      // العودة بأمان
      debugPrint('🏠 العودة للشاشة السابقة...');
      Navigator.of(context).pop();

      debugPrint('✅ تمت العودة بنجاح');

    } catch (e) {
      debugPrint('❌ خطأ في العودة الآمنة: $e');

      // في حالة الخطأ، العودة العادية مع إيقاف المؤقت
      if (mounted) {
        try {
          if (timer.isActive) {
            timer.cancel();
          }
          Navigator.of(context).pop();
          debugPrint('✅ تمت العودة الاحتياطية');
        } catch (e2) {
          debugPrint('❌ خطأ في العودة الاحتياطية: $e2');
        }
      }
    }
  }

  // حساب النجوم بناءً على عدد الإجابات الصحيحة
  int _calculateStars(int correctAnswers) {
    if (correctAnswers >= 10) return 3;
    if (correctAnswers >= 6) return 2;
    if (correctAnswers >= 3) return 1;
    return 0;
  }

  // حساب النقاط الحالية
  int _calculateScore() {
    int baseScore = correctAnswersCount * 100;
    int timeBonus = timeLeft * 2;
    int streakBonus = consecutiveCorrect * 50;
    return baseScore + timeBonus + streakBonus;
  }

  // بناء زر العودة مدمج وصغير - محسن مع حفظ آمن
  Widget _buildCompactBackButton() {
    return SizedBox(
      height: 45,
      child: ElevatedButton(
        onPressed: () async {
          await _safeNavigateBack();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.deepPurple.withOpacity(0.8),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(22),
          ),
          elevation: 4,
          shadowColor: Colors.deepPurple.withOpacity(0.5),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.arrow_back_ios,
              size: 16,
              color: Colors.white,
            ),
            const SizedBox(width: 4),
            const Flexible(
              child: Text(
                "عودة",
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء صف أزرار المساعدة المحسن مع تأثيرات إبداعية وواضحة
  Widget _buildEnhancedCompactHelpRow() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, animationValue, child) {
        return Transform.translate(
          offset: Offset(0, (1 - animationValue) * 20),
          child: Opacity(
            opacity: animationValue,
            child: Container(
              height: 55,
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: Stack(
                children: [
                  // خلفية متدرجة أكثر وضوح<|im_start|>
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.black.withOpacity(0.6),
                          Colors.black.withOpacity(0.4),
                          Colors.black.withOpacity(0.3),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(28),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.4),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.5),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                          spreadRadius: 2,
                        ),
                        BoxShadow(
                          color: Colors.white.withOpacity(0.3),
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                  // تأثير لمعان متحرك
                  Positioned.fill(
                    child: TweenAnimationBuilder<double>(
                      duration: const Duration(seconds: 2),
                      tween: Tween(begin: 0.0, end: 1.0),
                      builder: (context, value, child) {
                        return Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(28),
                            gradient: LinearGradient(
                              begin: Alignment(-1.0 + (value * 2), -1.0),
                              end: Alignment(1.0 + (value * 2), 1.0),
                              colors: [
                                Colors.transparent,
                                Colors.white.withOpacity(0.3),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  // الأزرار
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildEnhancedHelpButton(
                          icon: Icons.content_cut,
                          label: "50/50",
                          isAvailable: helpFiftyFiftyAvailable,
                          onPressed: useFiftyFifty,
                          color: const Color(0xFFE91E63),
                        ),
                        _buildEnhancedHelpButton(
                          icon: Icons.skip_next,
                          label: "تخطي",
                          isAvailable: helpSkipAvailable,
                          onPressed: useSkip,
                          color: const Color(0xFF2196F3),
                        ),
                        _buildEnhancedHelpButton(
                          icon: Icons.timer,
                          label: "+15ث",
                          isAvailable: helpExtraTimeAvailable,
                          onPressed: useExtraTime,
                          color: const Color(0xFF4CAF50),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء زر مساعدة محسن وواضح
  Widget _buildEnhancedHelpButton({
    required IconData icon,
    required String label,
    required bool isAvailable,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        height: 43,
        margin: const EdgeInsets.symmetric(horizontal: 3),
        child: ElevatedButton(
          onPressed: isAvailable ? onPressed : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: isAvailable ? color : Colors.grey.withOpacity(0.6),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(22),
            ),
            elevation: isAvailable ? 6 : 2,
            shadowColor: isAvailable ? color.withOpacity(0.6) : Colors.grey.withOpacity(0.3),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 18,
                color: Colors.white,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.w800,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.5),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء زر مساعدة بسيط وواضح
  Widget _buildSimpleHelpButton({
    required IconData icon,
    required String label,
    required bool isAvailable,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        height: 42,
        margin: const EdgeInsets.symmetric(horizontal: 3),
        child: ElevatedButton(
          onPressed: isAvailable ? onPressed : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: isAvailable ? color : Colors.grey.withOpacity(0.5),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(21),
            ),
            elevation: isAvailable ? 4 : 1,
            shadowColor: isAvailable ? color.withOpacity(0.5) : Colors.transparent,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  label,
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء زر مساعدة محسن مع تأثيرات جمالية
  Widget _buildEnhancedMiniHelpButton({
    required IconData icon,
    required String label,
    required bool isAvailable,
    required VoidCallback? onPressed,
    required Color color,
    required int delay,
  }) {
    return Expanded(
      child: TweenAnimationBuilder<double>(
        duration: Duration(milliseconds: 800 + delay),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, animationValue, child) {
          return Transform.scale(
            scale: 0.8 + (animationValue * 0.2),
            child: Opacity(
              opacity: animationValue,
              child: Container(
                height: 42,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  child: ElevatedButton(
                    onPressed: isAvailable ? onPressed : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isAvailable ? color : Colors.grey.withOpacity(0.4),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(21),
                      ),
                      elevation: isAvailable ? 4 : 1,
                      shadowColor: isAvailable ? color.withOpacity(0.5) : Colors.transparent,
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // تأثير لمعان للأزرار المتاحة
                        if (isAvailable)
                          Positioned.fill(
                            child: TweenAnimationBuilder<double>(
                              duration: const Duration(seconds: 2),
                              tween: Tween(begin: 0.0, end: 1.0),
                              builder: (context, value, child) {
                                return Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(21),
                                    gradient: LinearGradient(
                                      begin: Alignment(-1.0 + (value * 2), -1.0),
                                      end: Alignment(1.0 + (value * 2), 1.0),
                                      colors: [
                                        Colors.transparent,
                                        Colors.white.withOpacity(0.2),
                                        Colors.transparent,
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        // المحتوى
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            TweenAnimationBuilder<double>(
                              duration: const Duration(seconds: 1),
                              tween: Tween(begin: 0.0, end: 1.0),
                              builder: (context, value, child) {
                                return Transform.rotate(
                                  angle: isAvailable ? (math.sin(value * math.pi * 2) * 0.1) : 0,
                                  child: Icon(icon, size: 16),
                                );
                              },
                            ),
                            const SizedBox(width: 4),
                            Flexible(
                              child: Text(
                                label,
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w700,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: widget.levelData.backgroundGradient,
            ),
          ),
          child: Stack(
            children: [
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                  // ترويسة اللعبة
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          "المستوى ${widget.levelData.level}: ${widget.levelData.title}",
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (currentStreak > 1)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.amber.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              const Text(
                                "🔥",
                                style: TextStyle(fontSize: 16),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                "$currentStreak متتالية",
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFFFFDD00),
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(width: 8),
                      // مؤقت العد التنازلي
                      AnimatedBuilder(
                        animation: _timerAnimationController,
                        builder: (context, child) {
                          return Container(
                            width: 45,
                            height: 45,
                            decoration: BoxDecoration(
                              color: timeLeft <= 10
                                  ? const Color(0xFFE63946)
                                  : Colors.white.withOpacity(0.9),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: const Color(0xFFE63946),
                                width: 3,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                "$timeLeft",
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w900,
                                  color: timeLeft <= 10
                                      ? _timerColorAnimation.value ?? Colors.white
                                      : const Color(0xFFE63946),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // أزرار المساعدة وزر العودة في الأعلى لتجنب التداخل
                  _buildTopControlsRow(),
                  const SizedBox(height: 16),
                  // شريط التقدم المحسن مع أيقونات
                  _buildEnhancedProgressBar(),
                  const SizedBox(height: 24),
                  // نص السؤال مع إطار جميل
                  if (questionIndex < widget.levelData.questions.length)
                    _buildQuestionCard(),
                  const SizedBox(height: 16),
                  // خيارات الإجابة - مساحة كاملة بدون تداخل
                  Expanded(
                    child: questionIndex < widget.levelData.questions.length
                        ? _buildAnswerOptions()
                        : const SizedBox.shrink(),
                  ),
                    ],
                  ),
                ),
              ),
              // تأثيرات الجسيمات للنجاح
              if (showSuccessParticles)
                Positioned.fill(
                  child: IgnorePointer(
                    child: Container(
                      child: CustomPaint(
                        painter: ParticlesPainter(),
                        size: Size.infinite,
                      ),
                    ),
                  ),
                ),
              // تأثير الانفجار الخاص للإنجازات المتتالية
              if (showSuccessBurst)
                Positioned.fill(
                  child: IgnorePointer(
                    child: Container(
                      child: CustomPaint(
                        painter: BurstEffectPainter(),
                        size: Size.infinite,
                      ),
                    ),
                  ),
                ),
              // تم إزالة الرسالة المنبثقة للإجابات المتتالية
            ],
          ),
        ),
      ),
    );
  }

  // بناء زر مساعدة مدمج وصغير
  Widget _buildCompactMiniHelpButton({
    required IconData icon,
    required String label,
    required bool isAvailable,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        height: 35,
        margin: const EdgeInsets.symmetric(horizontal: 3),
        child: ElevatedButton(
          onPressed: isAvailable ? onPressed : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: isAvailable ? color : Colors.grey.withOpacity(0.5),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(18),
            ),
            elevation: isAvailable ? 3 : 1,
            shadowColor: isAvailable ? color.withOpacity(0.5) : Colors.transparent,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 14),
              const SizedBox(width: 3),
              Flexible(
                child: Text(
                  label,
                  style: const TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء زر العودة المحسن مع تأثيرات جمالية
  Widget _buildEnhancedBackButton() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, animationValue, child) {
        return Transform.translate(
          offset: Offset(0, (1 - animationValue) * 30),
          child: Opacity(
            opacity: animationValue,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Stack(
                children: [
                  // تأثير الخلفية المتوهجة
                  Container(
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.deepPurple.withOpacity(0.8),
                          Colors.indigo.withOpacity(0.6),
                          Colors.blue.withOpacity(0.4),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.deepPurple.withOpacity(0.4),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                          spreadRadius: 2,
                        ),
                        BoxShadow(
                          color: Colors.white.withOpacity(0.2),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1.5,
                      ),
                    ),
                  ),
                  // تأثير لمعان متحرك
                  Positioned.fill(
                    child: TweenAnimationBuilder<double>(
                      duration: const Duration(seconds: 2),
                      tween: Tween(begin: 0.0, end: 1.0),
                      builder: (context, value, child) {
                        return Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(25),
                            gradient: LinearGradient(
                              begin: Alignment(-1.0 + (value * 2), -1.0),
                              end: Alignment(1.0 + (value * 2), 1.0),
                              colors: [
                                Colors.transparent,
                                Colors.white.withOpacity(0.3),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  // الزر الفعلي
                  SizedBox(
                    height: 50,
                    child: ElevatedButton(
                      onPressed: () async {
                        await _safeNavigateBack();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: Colors.white,
                        shadowColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TweenAnimationBuilder<double>(
                            duration: const Duration(seconds: 1),
                            tween: Tween(begin: 0.0, end: 1.0),
                            builder: (context, value, child) {
                              return Transform.rotate(
                                angle: math.sin(value * math.pi * 2) * 0.1,
                                child: const Icon(
                                  Icons.arrow_back_ios,
                                  size: 18,
                                  color: Colors.white,
                                ),
                              );
                            },
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            "العودة للمستويات",
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 16,
                              shadows: [
                                Shadow(
                                  color: Colors.black38,
                                  blurRadius: 2,
                                  offset: Offset(0, 1),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء زر مساعدة متقدم مع تأثيرات إبداعية
  Widget _buildAdvancedMiniHelpButton({
    required IconData icon,
    required String label,
    required bool isAvailable,
    required VoidCallback? onPressed,
    required Color color,
    required int delay,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, animationValue, child) {
        return Transform.scale(
          scale: 0.8 + (animationValue * 0.2),
          child: Opacity(
            opacity: animationValue,
            child: Expanded(
              child: Container(
                height: 42,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                child: Stack(
                  children: [
                    // خلفية متوهجة
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: isAvailable ? [
                            color.withOpacity(0.8),
                            color.withOpacity(0.6),
                            color.withOpacity(0.4),
                          ] : [
                            Colors.grey.withOpacity(0.4),
                            Colors.grey.withOpacity(0.3),
                            Colors.grey.withOpacity(0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(21),
                        boxShadow: isAvailable ? [
                          BoxShadow(
                            color: color.withOpacity(0.4),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                            spreadRadius: 1,
                          ),
                          BoxShadow(
                            color: Colors.white.withOpacity(0.3),
                            blurRadius: 1,
                            offset: const Offset(0, 1),
                          ),
                        ] : [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                        border: Border.all(
                          color: isAvailable
                              ? Colors.white.withOpacity(0.4)
                              : Colors.grey.withOpacity(0.3),
                          width: 1.5,
                        ),
                      ),
                    ),
                    // تأثير نبضي للأزرار المتاحة
                    if (isAvailable)
                      Positioned.fill(
                        child: TweenAnimationBuilder<double>(
                          duration: const Duration(seconds: 2),
                          tween: Tween(begin: 0.0, end: 1.0),
                          builder: (context, value, child) {
                            return Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(21),
                                gradient: RadialGradient(
                                  center: Alignment.center,
                                  radius: 0.5 + (math.sin(value * math.pi * 2) * 0.2),
                                  colors: [
                                    color.withOpacity(0.3),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    // الزر الفعلي
                    SizedBox(
                      height: 42,
                      child: ElevatedButton(
                        onPressed: isAvailable ? onPressed : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          shadowColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(21),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            TweenAnimationBuilder<double>(
                              duration: const Duration(seconds: 1),
                              tween: Tween(begin: 0.0, end: 1.0),
                              builder: (context, value, child) {
                                return Transform.rotate(
                                  angle: isAvailable ? math.sin(value * math.pi * 2) * 0.1 : 0,
                                  child: Icon(
                                    icon,
                                    size: 16,
                                    color: isAvailable ? Colors.white : Colors.grey[400],
                                    shadows: isAvailable ? [
                                      const Shadow(
                                        color: Colors.black38,
                                        blurRadius: 2,
                                        offset: Offset(0, 1),
                                      ),
                                    ] : null,
                                  ),
                                );
                              },
                            ),
                            const SizedBox(width: 3),
                            Flexible(
                              child: Text(
                                label,
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w800,
                                  color: isAvailable ? Colors.white : Colors.grey[400],
                                  shadows: isAvailable ? [
                                    const Shadow(
                                      color: Colors.black38,
                                      blurRadius: 2,
                                      offset: Offset(0, 1),
                                    ),
                                  ] : null,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    timer.cancel();
    _timerAnimationController.dispose();
    _questionAnimationController.dispose();
    super.dispose();
  }
}

class OptionButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isCorrectAnswer;
  final bool isIncorrectSelected;

  const OptionButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isCorrectAnswer = false,
    this.isIncorrectSelected = false,
  });

  @override
  State<OptionButton> createState() => _OptionButtonState();
}

class _OptionButtonState extends State<OptionButton> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      vsync: this, 
      duration: const Duration(milliseconds: 500),
    );
    
    _shakeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    if (widget.isIncorrectSelected) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(OptionButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isIncorrectSelected && !oldWidget.isIncorrectSelected) {
      _animationController.repeat(reverse: true);
    } else if (!widget.isIncorrectSelected && oldWidget.isIncorrectSelected) {
      _animationController.stop();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color borderColor;
    IconData? statusIcon;

    if (widget.isCorrectAnswer) {
      backgroundColor = const Color(0xFF4CAF50);
      borderColor = const Color(0xFF81C784);
      statusIcon = Icons.check_circle;
    } else if (widget.isIncorrectSelected) {
      backgroundColor = const Color(0xFFE53935);
      borderColor = const Color(0xFFEF5350);
      statusIcon = Icons.cancel;
    } else {
      backgroundColor = const Color(0xFF2196F3);
      borderColor = const Color(0xFF64B5F6);
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        double dx = widget.isIncorrectSelected
            ? sin(_animationController.value * 2 * pi) * 4
            : 0;

        return Transform.translate(
          offset: Offset(dx, 0),
          child: child,
        );
      },
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              backgroundColor,
              backgroundColor.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: borderColor, width: 2),
          boxShadow: [
            BoxShadow(
              color: backgroundColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: widget.onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.white,
            shadowColor: Colors.transparent,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Row(
            children: [
              if (statusIcon != null) ...[
                Icon(
                  statusIcon,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Text(
                  widget.text,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HelpButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isAvailable;
  final VoidCallback? onPressed;

  const HelpButton({
    super.key,
    required this.icon,
    required this.label,
    required this.isAvailable,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: isAvailable ? onPressed : null,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: isAvailable
            ? const Color(0xFFFB8500)
            : Colors.grey.withOpacity(0.5),
        foregroundColor: Colors.white,
        disabledBackgroundColor: Colors.grey.withOpacity(0.5),
        disabledForegroundColor: Colors.white.withOpacity(0.6),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isAvailable ? Colors.white : Colors.grey,
            width: 2,
          ),
        ),
      ),
    );
  }
}












// شاشة الفوز الجديدة
class VictoryScreen extends StatefulWidget {
  final LevelModel levelData;
  final int score;
  final int starsEarned;
  final int correctAnswers;
  final AudioService audioService;
  final Function(int, int) onLevelComplete;
  final VoidCallback? onDataUpdated; // إضافة callback لتحديث البيانات

  const VictoryScreen({
    super.key,
    required this.levelData,
    required this.score,
    required this.starsEarned,
    required this.correctAnswers,
    required this.audioService,
    required this.onLevelComplete,
    this.onDataUpdated, // إضافة المعامل الاختياري
  });

  @override
  State<VictoryScreen> createState() => _VictoryScreenState();
}

class _VictoryScreenState extends State<VictoryScreen>
    with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _starsController;
  late AnimationController _scoreController;

  late Animation<double> _celebrationAnimation;
  late Animation<double> _starsAnimation;
  late Animation<int> _scoreAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد الأنيميشن
    _celebrationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _starsController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _celebrationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _celebrationController,
      curve: Curves.elasticOut,
    ));

    _starsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _starsController,
      curve: Curves.bounceOut,
    ));

    _scoreAnimation = IntTween(
      begin: 0,
      end: widget.score,
    ).animate(CurvedAnimation(
      parent: _scoreController,
      curve: Curves.easeOut,
    ));

    // بدء الأنيميشن
    _startAnimations();

    // حفظ التقدم
    widget.onLevelComplete(widget.starsEarned, widget.score);
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _celebrationController.forward();

    await Future.delayed(const Duration(milliseconds: 500));
    _starsController.forward();

    await Future.delayed(const Duration(milliseconds: 800));
    _scoreController.forward();
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _starsController.dispose();
    _scoreController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.purple.shade900,
                Colors.purple.shade700,
                Colors.blue.shade800,
                Colors.blue.shade900,
              ],
            ),
          ),
          child: SafeArea(
            child: Stack(
              children: [
                // تأثيرات الخلفية المتحركة (مبسطة)
                _buildBackgroundEffects(),

                // المحتوى الرئيسي
                Center(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // عنوان الفوز (مصغر)
                        _buildVictoryTitle(),

                        const SizedBox(height: 20),

                        // معلومات المستوى (مصغرة)
                        _buildLevelInfo(),

                        const SizedBox(height: 20),

                        // النجوم (مصغرة)
                        _buildStarsDisplay(),

                        const SizedBox(height: 20),

                        // النقاط (مصغرة)
                        _buildScoreDisplay(),

                        const SizedBox(height: 30),

                        // الأزرار (مصغرة)
                        _buildActionButtons(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // تأثيرات الخلفية المتحركة (مبسطة)
  Widget _buildBackgroundEffects() {
    return AnimatedBuilder(
      animation: _celebrationAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            // جسيمات متحركة (أقل عدداً)
            ...List.generate(8, (index) {
              final double x = (index * 80.0) % MediaQuery.of(context).size.width;
              final double y = (index * 120.0) % MediaQuery.of(context).size.height;

              return Positioned(
                left: x + (math.sin(_celebrationAnimation.value * math.pi * 2 + index) * 20),
                top: y + (math.cos(_celebrationAnimation.value * math.pi * 2 + index) * 15),
                child: Opacity(
                  opacity: 0.2 + (math.sin(_celebrationAnimation.value * math.pi * 4 + index) * 0.1),
                  child: Icon(
                    index % 2 == 0 ? Icons.star : Icons.celebration,
                    color: [Colors.amber, Colors.cyan][index % 2],
                    size: 12 + (math.sin(_celebrationAnimation.value * math.pi * 2 + index) * 3),
                  ),
                ),
              );
            }),
          ],
        );
      },
    );
  }

  // عنوان الفوز
  Widget _buildVictoryTitle() {
    return AnimatedBuilder(
      animation: _celebrationAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.5 + (_celebrationAnimation.value * 0.5),
          child: Opacity(
            opacity: _celebrationAnimation.value,
            child: Column(
              children: [
                // أيقونة الفوز (مصغرة)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Colors.amber.shade300,
                        Colors.orange.shade500,
                        Colors.red.shade600,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.amber.withOpacity(0.3),
                        blurRadius: 15,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.emoji_events,
                    size: 40,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 12),

                // نص الفوز (مصغر)
                Text(
                  '🎉 تهانينا! 🎉',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.5),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 6),

                Text(
                  'لقد أكملت المستوى بنجاح!',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // معلومات المستوى (مصغرة)
  Widget _buildLevelInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.flag, color: Colors.white, size: 18),
              const SizedBox(width: 6),
              Text(
                'المستوى ${widget.levelData.level}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 16),
              const SizedBox(width: 6),
              Text(
                'إجابات صحيحة: ${widget.correctAnswers}/10',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // عرض النجوم (مصغرة)
  Widget _buildStarsDisplay() {
    return AnimatedBuilder(
      animation: _starsAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _starsAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.1),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.amber.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                const Text(
                  'النجوم المكتسبة',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 8),

                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(3, (index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      child: AnimatedContainer(
                        duration: Duration(milliseconds: 500 + (index * 200)),
                        child: Icon(
                          Icons.star,
                          size: 28,
                          color: index < widget.starsEarned
                              ? Colors.amber
                              : Colors.white.withOpacity(0.3),
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // عرض النقاط (مصغرة)
  Widget _buildScoreDisplay() {
    return AnimatedBuilder(
      animation: _scoreAnimation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: Colors.blue.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              const Text(
                'النقاط المكتسبة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 8),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.stars, color: Colors.blue, size: 20),
                  const SizedBox(width: 6),
                  Text(
                    '${_scoreAnimation.value}',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    'نقطة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // أزرار العمل (مصغرة)
  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر المستوى التالي
        _buildActionButton(
          text: 'المستوى التالي',
          icon: Icons.arrow_forward,
          emoji: '➡️',
          color: Colors.green,
          onPressed: _goToNextLevel,
        ),

        const SizedBox(height: 10),

        Row(
          children: [
            // زر إعادة اللعب
            Expanded(
              child: _buildActionButton(
                text: 'إعادة اللعب',
                icon: Icons.replay,
                emoji: '🔄',
                color: Colors.orange,
                onPressed: _restartLevel,
              ),
            ),

            const SizedBox(width: 10),

            // زر العودة للرئيسية
            Expanded(
              child: _buildActionButton(
                text: 'الرئيسية',
                icon: Icons.home,
                emoji: '🏠',
                color: Colors.purple,
                onPressed: _goToHome,
              ),
            ),
          ],
        ),
      ],
    );
  }
  // بناء زر العمل (مصغر)
  Widget _buildActionButton({
    required String text,
    required IconData icon,
    required String emoji,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 3,
        shadowColor: color.withOpacity(0.3),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(width: 4),
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // الانتقال للمستوى التالي
  void _goToNextLevel() async {
    try {
      // حفظ التقدم وفتح المستوى التالي
      await _saveProgressAndUnlockNext();

      if (mounted) {
        // العودة لشاشة المستويات الأصلية (إزالة شاشة الفوز والعودة للشاشة السابقة)
        Navigator.of(context).pop();

        // تحديث البيانات في الشاشة السابقة
        if (widget.onDataUpdated != null) {
          widget.onDataUpdated!();
        }

        // عرض رسالة نجاح بعد تأخير قصير للسماح بالتحديث
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.celebration, color: Colors.white),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '🎉 تم فتح المستوى ${widget.levelData.level + 1}! يمكنك الآن اللعب',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في الانتقال للمستوى التالي: $e');
    }
  }

  // إعادة تشغيل المستوى
  void _restartLevel() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => GameScreen(
          levelData: widget.levelData,
          audioService: widget.audioService,
          onLevelComplete: widget.onLevelComplete,
          onDataUpdated: widget.onDataUpdated, // تمرير callback
        ),
      ),
    );
  }

  // العودة للشاشة الرئيسية
  void _goToHome() {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  // حفظ التقدم وفتح المستوى التالي
  Future<void> _saveProgressAndUnlockNext() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String key = '';

      // تحديد مفتاح التخزين حسب الفئة
      switch (widget.levelData.category) {
        case QuizCategory.islamic:
          key = 'islamic_levels_data';
          break;
        case QuizCategory.math:
          key = 'math_levels_data';
          break;
        case QuizCategory.historical:
          key = 'historical_levels_data';
          break;
        case QuizCategory.videoGames:
          key = 'video_games_levels_data';
          break;
        case QuizCategory.sports:
          key = 'sports_levels_data';
          break;
        case QuizCategory.literature:
          key = 'literature_levels_data';
          break;
        case QuizCategory.animals:
          key = 'animals_levels_data';
          break;
        case QuizCategory.plants:
          key = 'plants_levels_data';
          break;
        case QuizCategory.cars:
          key = 'cars_levels_data';
          break;
      }

      if (key.isNotEmpty) {
        // تحميل البيانات الحالية
        final String? levelsDataString = prefs.getString(key);
        List<Map<String, dynamic>> savedLevels = [];

        if (levelsDataString != null) {
          final List<dynamic> decodedData = jsonDecode(levelsDataString);
          savedLevels = decodedData.cast<Map<String, dynamic>>();
        }

        // تحديث بيانات المستوى الحالي
        for (int i = 0; i < savedLevels.length; i++) {
          if (savedLevels[i]['level'] == widget.levelData.level) {
            // تحديث النجوم والنقاط
            if (widget.starsEarned > (savedLevels[i]['stars'] ?? 0)) {
              savedLevels[i]['stars'] = widget.starsEarned;
            }
            if (widget.score > (savedLevels[i]['highScore'] ?? 0)) {
              savedLevels[i]['highScore'] = widget.score;
            }

            // فتح المستوى التالي إذا حصل على نجمة
            if (widget.starsEarned > 0 && i + 1 < savedLevels.length) {
              savedLevels[i + 1]['isUnlocked'] = true;
              debugPrint('🔓 تم فتح المستوى ${savedLevels[i + 1]['level']}');

              // حفظ معلومة المستوى المفتوح حديثاً لإظهار التوهج
              await prefs.setInt('recently_unlocked_${widget.levelData.category.name}', savedLevels[i + 1]['level']);
            }

            break;
          }
        }

        // حفظ البيانات المحدثة
        await prefs.setString(key, jsonEncode(savedLevels));
        await prefs.setInt('${key}_timestamp', DateTime.now().millisecondsSinceEpoch);

        // تحديث البيانات في الذاكرة أيضاً لضمان التحديث الفوري
        await _updateInMemoryLevels(savedLevels);

        debugPrint('✅ تم حفظ التقدم وفتح المستوى التالي بنجاح');
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ التقدم: $e');
    }
  }

  // تحديث البيانات في الذاكرة
  Future<void> _updateInMemoryLevels(List<Map<String, dynamic>> savedLevels) async {
    try {
      // العثور على المستويات في allLevels وتحديثها
      for (var savedLevel in savedLevels) {
        final levelIndex = allLevels.indexWhere((level) =>
          level.category == widget.levelData.category &&
          level.level == savedLevel['level']
        );

        if (levelIndex != -1) {
          // تحديث البيانات مباشرة
          allLevels[levelIndex].stars = savedLevel['stars'] ?? 0;
          allLevels[levelIndex].highScore = savedLevel['highScore'] ?? 0;
          allLevels[levelIndex].isUnlocked = savedLevel['isUnlocked'] ?? false;
        }
      }

      debugPrint('✅ تم تحديث البيانات في الذاكرة');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات في الذاكرة: $e');
    }
  }


}

// خدمة الصوت
class AudioService {
  AudioPlayer? _clickPlayer;
  AudioPlayer? _correctPlayer;
  AudioPlayer? _incorrectPlayer;
  AudioPlayer? _streakPlayer;
  AudioPlayer? _levelCompletePlayer;
  AudioPlayer? _timerWarningPlayer;

  bool _isInitialized = false;
  bool _soundEnabled = true;

  Future<void> initialize() async {
    if (_isInitialized) return;

    _clickPlayer = AudioPlayer();
    _correctPlayer = AudioPlayer();
    _incorrectPlayer = AudioPlayer();
    _streakPlayer = AudioPlayer();
    _levelCompletePlayer = AudioPlayer();
    _timerWarningPlayer = AudioPlayer();

    _isInitialized = true;
  }

  void playClickSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    try {
      await _clickPlayer?.play(AssetSource('audio/click.mp3'));
    } catch (e) {
      print('Error playing click sound: $e');
    }
  }

  void playCorrectSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    try {
      await _correctPlayer?.play(AssetSource('audio/correct.mp3'));
    } catch (e) {
      print('Error playing correct sound: $e');
    }
  }
  
  void playStreakSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    try {
      await _streakPlayer?.play(AssetSource('audio/streak.mp3'));
    } catch (e) {
      print('Error playing streak sound: $e');
    }
  }

  void playIncorrectSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    try {
      await _incorrectPlayer?.play(AssetSource('audio/incorrect.mp3'));
    } catch (e) {
      print('Error playing incorrect sound: $e');
    }
  }

  void playLevelCompleteSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    try {
      await _levelCompletePlayer?.play(AssetSource('audio/level_complete.mp3'));
    } catch (e) {
      print('Error playing level complete sound: $e');
    }
  }

  void playTimerWarningSound() async {
    if (!_soundEnabled || !_isInitialized) return;
    try {
      await _timerWarningPlayer?.play(AssetSource('audio/timer_warning.mp3'));
    } catch (e) {
      print('Error playing timer warning sound: $e');
    }
  }

  void toggleSound() {
    _soundEnabled = !_soundEnabled;
  }

  bool get isSoundEnabled => _soundEnabled;
  
  void dispose() {
    _clickPlayer?.dispose();
    _correctPlayer?.dispose();
    _incorrectPlayer?.dispose();
    _streakPlayer?.dispose();
    _levelCompletePlayer?.dispose();
    _timerWarningPlayer?.dispose();

    _isInitialized = false;
  }
}

// فئة رسم الجسيمات للتأثيرات البصرية
class ParticlesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.amber
      ..style = PaintingStyle.fill;

    // رسم جسيمات عشوائية متحركة
    for (int i = 0; i < 15; i++) {
      final x = (i * 80.0 + DateTime.now().millisecondsSinceEpoch * 0.01) % size.width;
      final y = (i * 60.0 + DateTime.now().millisecondsSinceEpoch * 0.005) % size.height;

      // ألوان متنوعة للجسيمات
      final colors = [Colors.amber, Colors.orange, Colors.yellow, Colors.red];
      paint.color = colors[i % colors.length];

      canvas.drawCircle(Offset(x, y), 4.0, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

// فئة رسم تأثير الانفجار للإنجازات المتتالية
class BurstEffectPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final time = DateTime.now().millisecondsSinceEpoch * 0.005;

    // رسم انفجار من النجوم
    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0) * (3.14159 / 180.0) + time;
      final radius = 50.0 + (time % 100);

      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);

      // ألوان ذهبية متدرجة
      final colors = [
        Colors.amber,
        Colors.yellow,
        Colors.orange,
        Colors.deepOrange,
      ];
      paint.color = colors[i % colors.length];

      // رسم نجمة
      _drawStar(canvas, paint, Offset(x, y), 8.0);
    }
  }

  void _drawStar(Canvas canvas, Paint paint, Offset center, double size) {
    final path = Path();
    final outerRadius = size;
    final innerRadius = size * 0.4;

    for (int i = 0; i < 10; i++) {
      final angle = (i * 36.0) * (3.14159 / 180.0);
      final radius = i % 2 == 0 ? outerRadius : innerRadius;
      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
