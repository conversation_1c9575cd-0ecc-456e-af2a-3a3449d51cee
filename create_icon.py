#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create app icons for Android from SVG
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, output_path):
    """Create an app icon with the specified size"""
    # Create a new image with a blue gradient background
    img = Image.new('RGBA', (size, size), (3, 57, 108, 255))
    draw = ImageDraw.Draw(img)
    
    # Draw gradient background (simplified)
    for y in range(size):
        alpha = int(255 * (1 - y / size * 0.3))
        color = (3 + int(y / size * 50), 57 + int(y / size * 100), 108 + int(y / size * 50), alpha)
        draw.line([(0, y), (size, y)], fill=color)
    
    # Draw a book shape
    book_width = int(size * 0.6)
    book_height = int(size * 0.7)
    book_x = (size - book_width) // 2
    book_y = (size - book_height) // 2
    
    # Book background
    draw.rounded_rectangle(
        [book_x, book_y, book_x + book_width, book_y + book_height],
        radius=size//20,
        fill=(252, 163, 17, 255)  # Orange color
    )
    
    # Book spine
    spine_width = book_width // 8
    draw.rounded_rectangle(
        [book_x, book_y, book_x + spine_width, book_y + book_height],
        radius=size//20,
        fill=(232, 93, 0, 255)  # Darker orange
    )
    
    # Book pages
    page_margin = size // 20
    draw.rounded_rectangle(
        [book_x + spine_width + page_margin, book_y + page_margin, 
         book_x + book_width - page_margin, book_y + book_height - page_margin],
        radius=size//40,
        fill=(255, 255, 255, 255)  # White pages
    )
    
    # Draw question mark
    try:
        # Try to use a font, fallback to default if not available
        font_size = size // 4
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    # Question mark
    question_mark = "؟"
    bbox = draw.textbbox((0, 0), question_mark, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = (size - text_width) // 2
    text_y = book_y + book_height - int(size * 0.25)
    
    draw.text((text_x, text_y), question_mark, fill=(3, 57, 108, 255), font=font)
    
    # Save the image
    img.save(output_path, 'PNG')
    print(f"Created icon: {output_path} ({size}x{size})")

def main():
    """Create all required Android icon sizes"""
    # Android icon sizes
    sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    base_path = "android/app/src/main/res"
    
    for folder, size in sizes.items():
        folder_path = os.path.join(base_path, folder)
        os.makedirs(folder_path, exist_ok=True)
        icon_path = os.path.join(folder_path, "ic_launcher.png")
        create_icon(size, icon_path)

if __name__ == "__main__":
    main()
