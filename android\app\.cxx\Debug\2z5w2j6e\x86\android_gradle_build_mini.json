{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\alll\\musabkat7\\android\\app\\.cxx\\Debug\\2z5w2j6e\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\alll\\musabkat7\\android\\app\\.cxx\\Debug\\2z5w2j6e\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}