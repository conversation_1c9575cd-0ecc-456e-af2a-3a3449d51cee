<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد أيقونة التطبيق</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .icon {
            width: 192px;
            height: 192px;
            background: linear-gradient(135deg, #03396C 0%, #005AA7 100%);
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }
        
        .icon::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 140px;
            background: linear-gradient(135deg, #FCA311 0%, #F79D00 100%);
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }
        
        .icon::after {
            content: '';
            position: absolute;
            top: 35px;
            left: 50%;
            transform: translateX(-50%);
            width: 90px;
            height: 110px;
            background: white;
            border-radius: 4px;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .question-mark {
            position: relative;
            z-index: 10;
            font-size: 48px;
            font-weight: bold;
            color: #03396C;
            margin-top: 60px;
        }
        
        .title {
            position: absolute;
            top: 10px;
            font-size: 14px;
            font-weight: bold;
            color: white;
            z-index: 10;
        }
        
        .download-btn {
            background: #03396C;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            font-family: inherit;
        }
        
        .download-btn:hover {
            background: #005AA7;
        }
        
        .sizes {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .size-icon {
            background: linear-gradient(135deg, #03396C 0%, #005AA7 100%);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .size-48 { width: 48px; height: 48px; }
        .size-72 { width: 72px; height: 72px; }
        .size-96 { width: 96px; height: 96px; }
        .size-144 { width: 144px; height: 144px; }
        
        .size-icon::before {
            content: '';
            position: absolute;
            top: 10%;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 70%;
            background: linear-gradient(135deg, #FCA311 0%, #F79D00 100%);
            border-radius: 2px;
        }
        
        .size-icon::after {
            content: '';
            position: absolute;
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
            width: 45%;
            height: 60%;
            background: white;
            border-radius: 1px;
        }
        
        .size-icon .mini-question {
            position: relative;
            z-index: 10;
            font-weight: bold;
            color: #03396C;
            margin-top: 20%;
        }
        
        .size-48 .mini-question { font-size: 12px; }
        .size-72 .mini-question { font-size: 18px; }
        .size-96 .mini-question { font-size: 24px; }
        .size-144 .mini-question { font-size: 36px; }
    </style>
</head>
<body>
    <div class="icon-container">
        <h1>أيقونة تطبيق المسابقات التعليمية</h1>
        
        <div class="icon">
            <div class="title">مسابقة</div>
            <div class="question-mark">؟</div>
        </div>
        
        <button class="download-btn" onclick="downloadIcon()">تحميل الأيقونة الرئيسية</button>
        
        <h2>أحجام مختلفة للأندرويد</h2>
        <div class="sizes">
            <div class="size-icon size-48" data-size="48">
                <div class="mini-question">؟</div>
            </div>
            <div class="size-icon size-72" data-size="72">
                <div class="mini-question">؟</div>
            </div>
            <div class="size-icon size-96" data-size="96">
                <div class="mini-question">؟</div>
            </div>
            <div class="size-icon size-144" data-size="144">
                <div class="mini-question">؟</div>
            </div>
        </div>
        
        <button class="download-btn" onclick="downloadAllSizes()">تحميل جميع الأحجام</button>
    </div>

    <script>
        function downloadIcon() {
            // This would require html2canvas library for actual implementation
            alert('لتحميل الأيقونة، يمكنك أخذ لقطة شاشة للأيقونة أعلاه أو استخدام أدوات التصميم المتاحة.');
        }
        
        function downloadAllSizes() {
            alert('لتحميل جميع الأحجام، يمكنك أخذ لقطات شاشة للأيقونات المختلفة أو استخدام أدوات التصميم المتاحة.');
        }
    </script>
</body>
</html>
