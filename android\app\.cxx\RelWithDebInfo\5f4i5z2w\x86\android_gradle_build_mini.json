{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\alll\\musabkat7\\android\\app\\.cxx\\RelWithDebInfo\\5f4i5z2w\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\alll\\musabkat7\\android\\app\\.cxx\\RelWithDebInfo\\5f4i5z2w\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}